import path from "path";

const config = {
  projectName: "low-altitude-flight-app",
  date: "2025-4-7",
  // designWidth: 390,
  designWidth(input) {
    if (input?.file?.replace(/\\+/g, "/")?.indexOf("taro-ui/dist") > -1) {
      return 780;
    }

    // 配置 NutUI 390 尺寸
    if (input?.file?.replace(/\\+/g, "/").indexOf("@nutui") > -1) {
      return 390;
    }

    return 390;
  },
  deviceRatio: {
    390: 2,
    780: 1,
    640: 2.43 / 2,
    828: 1.88 / 2,
    // 640: 2.34 / 2,
    //   750: 1,
    //   828: 1.81 / 2,
  },
  sourceRoot: "src",
  outputRoot: "dist",
  plugins: ["@tarojs/plugin-html"],
  defineConstants: {},
  copy: {
    patterns: [],
    options: {},
  },
  framework: "react",
  compiler: {
    type: "webpack5",
    // 仅 webpack5 支持依赖预编译配置
    prebundle: {
      enable: false,
    },
  },
  cache: {
    enable: false, // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
  },
  mini: {
    postcss: {
      pxtransform: {
        enable: true,
        config: {},
        // 包含 `nut-` 的类名选择器中的 px 单位不会被解析
        // config: { selectorBlackList: ["nut-"] },
      },
      url: {
        enable: true,
        config: {
          limit: 1024, // 设定转换尺寸上限
        },
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: "module", // 转换模式，取值为 global/module
          generateScopedName: "[name]__[local]___[hash:base64:5]",
        },
      },
    },
  },
  h5: {
    publicPath: "/",
    staticDirectory: "static",
    postcss: {
      autoprefixer: {
        enable: true,
        config: {},
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: "module", // 转换模式，取值为 global/module
          generateScopedName: "[name]__[local]___[hash:base64:5]",
        },
      },
    },
  },
  alias: {
    "@": path.resolve(__dirname, "..", "src"),
    // "@images": path.resolve(__dirname, "..", "src/assets/images"),
  },
};

module.exports = function (merge) {
  if (process.env.NODE_ENV === "development") {
    return merge({}, config, require("./dev"));
  }
  return merge({}, config, require("./prod"));
};
