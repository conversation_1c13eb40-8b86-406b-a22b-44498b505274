# 通航低空行项目

### UI & UX 地址
figma地址 https://www.figma.com/design/j0WvsGs6VMZlk3kMjogjjy/%E9%80%9A%E8%88%AA%C2%B7%E4%BD%8E%E7%A9%BA%E8%A1%8C?node-id=0-1&p=f&t=baS9Q0yefUbr5zn1-0

### NutUI
https://nutui.jd.com/taro/react/3x/#/zh-CN/guide/intro-react

###  主题
变量配置： src/styles/variables.less

### 接口地址

## 项目配置

### 项目安装
需要node 16+

#### node-module包安装
```
pnpm install
```
### 项目启动
小程序:`npm run dev:weapp`

### 项目打包
小程序:`npm run build:weapp`

### 静态图片处理
在`src/assets/images`目录下添加图片，在`src/utils/img.ts`中引入即可使用
