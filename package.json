{"name": "low-altitude-flight-app", "version": "1.0.0", "private": true, "description": "通航低空行", "templateInfo": {"name": "taro-ui", "typescript": true, "css": "less"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,less,css,json}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,less,css,json}\"", "swaggerUi": "node swaggerUi"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@nutui/icons-react-taro": "^3.0.1", "@nutui/nutui-react-taro": "^3.0.8", "@tarojs/components": "3.6.19", "@tarojs/helper": "3.6.19", "@tarojs/plugin-framework-react": "3.6.19", "@tarojs/plugin-html": "3.6.19", "@tarojs/plugin-platform-alipay": "3.6.19", "@tarojs/plugin-platform-h5": "3.6.19", "@tarojs/plugin-platform-jd": "3.6.19", "@tarojs/plugin-platform-qq": "3.6.19", "@tarojs/plugin-platform-swan": "3.6.19", "@tarojs/plugin-platform-tt": "3.6.19", "@tarojs/plugin-platform-weapp": "3.6.19", "@tarojs/react": "3.6.19", "@tarojs/runtime": "3.6.19", "@tarojs/shared": "3.6.19", "@tarojs/taro": "3.6.19", "axios": "^1.9.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "lodash": "4.17.15", "postcss": "^8.5.3", "react": "^18.0.0", "react-dom": "^18.0.0", "taro-ui": "^3.2.1"}, "devDependencies": {"@babel/core": "^7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.19", "@tarojs/taro-loader": "3.6.19", "@tarojs/webpack5-runner": "3.6.19", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "babel-preset-taro": "3.6.19", "eslint": "^8.12.0", "eslint-config-taro": "3.6.19", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "react-refresh": "^0.11.0", "stylelint": "9.3.0", "swagger-typescript-api": "12.0.3", "ts-node": "^10.9.1", "typescript": "^4.1.0", "webpack": "^5.78.0"}}