@import './styles/variables.less';

page {
  font-size: 14px;
  color: @color-text-primary;
  background: #fafafa;
}

view {
  box-sizing: border-box;
}

image {
  width: 100%;
  display: block;
}

Button {
  font-size: 16px;
  &::before,
  &::after {
    border: none;
  }
}
//.at-icon {
//  transform: scale(0.5);
//}
wx-button[type='primary'] {
  background-color: @color-primary;
  color: #fff;
}

//.nut-tabs-titles-item-active {
//  height: 44px;
//  position: relative;
//  &::after {
//    content: "";
//    position: absolute;
//    bottom: 0;
//    left: calc(50% - 32px);
//    width: 64px;
//    height: 4px;
//    border-radius: 12px;
//    background: linear-gradient(
//      90.6deg,
//      rgba(18, 64, 114, 0) 6.12%,
//      #124072 91.87%
//    );
//  }
//}
