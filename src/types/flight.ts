/**
 * 航班段数据结构
 * 用于统一管理单程、往返、多程航线的数据
 */
export interface FlightSegmentProps {
  /** 到达机场名称 */
  arrName: string;
  /** 到达机场代码 */
  arrCode: string;
  /** 出发机场名称 */
  depName: string;
  /** 出发机场代码 */
  depCode: string;
  /** 航班日期 YYYY-MM-DD */
  flightDate: string;
}

/**
 * 搜索航线组件的属性
 */
export interface SearchAirlineProps {
  /** 航线类型 */
  type?: 'oneWay' | 'roundTrip' | 'multipass';
  /** 获取数据的回调函数 */
  getData?: (data: FlightSegmentProps[]) => void;
}
