export default defineAppConfig({
  // entryPagePath: "",
  pages: [
    'pages/home/<USER>', //首页
    'pages/login/index', //登录
    'pages/verCodeLogin/index', //验证码登录
    'pages/product/index', //产品
    'pages/product/airTourDetail/index', //产品-空中游览-详情
    'pages/product/airTourOrderDetail/index', // 产品-空中游览-订单详情
    'pages/orders/travelOrders/refundDetails/index', // 产品-空中游览-退款详情
    'pages/orders/travelOrders/aerialTour/index', // 产品-空中游览-改期
    'pages/orders/index', //订单
    'pages/orders/travelOrders/orderDetails/index', // 旅游订单 - 订单详情
    'pages/orders/lowAirLineOrder/detail/index', // 低空航线订单 - 订单详情
    'pages/orders/lowAirLineOrder/changeDate/index', // 低空航线订单 - 订单改期
    'pages/userCenter/index', //我的
    'pages/userCenter/userInfo/index', //用户信息
    'pages/lowAltitudeAirline/index', //低空航线
    'pages/lowAltitudeAirline/detail/index', //低空航线-详情
    'pages/invoice/index', //发票
    'pages/jauntAirLine/index', //短途航线
    'pages/jauntAirLine/flightList/index', //短途航线-航班列表
    'pages/jauntAirLine/selectCabin/index', //短途航线-选择舱位
    'pages/jauntAirLine/passengerInfo/index', //短途航线-旅客信息
    'pages/jauntAirLine/addPassenger/index', //短途航线-添加乘机人
    'pages/jauntAirLine/payment/index', //短途航线-支付
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
    navigationStyle: 'custom',
    backgroundColor: '#FFCA67',
  },
  requiredPrivateInfos: [
    'getLocation', // 地址管理
  ],
  permission: {
    'scope.userLocation': {
      desc: '初始化时获取用户的位置信息用于推荐当前城市产品',
    },
  },
  tabBar: {
    // custom: true,
    color: '#737578',
    selectedColor: '#1663F8', // 主题色
    backgroundColor: 'red',
    list: [
      {
        pagePath: 'pages/home/<USER>',
        text: '首页',
        iconPath: 'assets/images/navMenu/home-icon.png',
        selectedIconPath: 'assets/images/navMenu/home-icon-active.png',
      },
      {
        pagePath: 'pages/orders/index',
        text: '订单',
        iconPath: 'assets/images/navMenu/order-icon.png',
        selectedIconPath: 'assets/images/navMenu/order-icon-active.png',
      },
      {
        pagePath: 'pages/userCenter/index',
        text: '我的',
        iconPath: 'assets/images/navMenu/my-icon.png',
        selectedIconPath: 'assets/images/navMenu/my-icon-active.png',
      },
    ],
  },
});
