/**
 * URL参数工具函数使用示例
 * 
 * 这个文件展示了如何使用封装的URL参数获取方法
 */

import { getUrlParams, getUrlParam, getCurrentPageUrl, buildUrlWithParams } from '@/utils';

// ===== 使用示例 =====

// 1. 获取当前页面的所有URL参数
export const example1 = () => {
  // 假设当前页面URL为: /pages/detail/index?id=123&name=test&type=flight
  const allParams = getUrlParams();
  console.log('所有参数:', allParams);
  // 输出: { id: '123', name: 'test', type: 'flight' }
};

// 2. 获取指定的URL参数
export const example2 = () => {
  // 获取单个参数
  const id = getUrlParam('id');
  const name = getUrlParam('name');
  const nonExistent = getUrlParam('notExist');
  
  console.log('ID:', id);           // 输出: '123'
  console.log('Name:', name);       // 输出: 'test'
  console.log('不存在的参数:', nonExistent); // 输出: null
};

// 3. 从指定URL获取参数
export const example3 = () => {
  const customUrl = '/pages/search/index?from=beijing&to=shanghai&date=2024-01-01';
  
  // 获取指定URL的所有参数
  const params = getUrlParams(customUrl);
  console.log('自定义URL参数:', params);
  // 输出: { from: 'beijing', to: 'shanghai', date: '2024-01-01' }
  
  // 获取指定URL的单个参数
  const from = getUrlParam('from', customUrl);
  console.log('出发地:', from); // 输出: 'beijing'
};

// 4. 获取当前页面完整URL
export const example4 = () => {
  const currentUrl = getCurrentPageUrl();
  console.log('当前页面URL:', currentUrl);
  // 输出: /pages/detail/index?id=123&name=test&type=flight
};

// 5. 构建带参数的URL
export const example5 = () => {
  const baseUrl = '/pages/flight/list';
  const params = {
    from: '北京',
    to: '上海',
    date: '2024-01-01',
    passengers: 2
  };
  
  const fullUrl = buildUrlWithParams(baseUrl, params);
  console.log('构建的URL:', fullUrl);
  // 输出: /pages/flight/list?from=%E5%8C%97%E4%BA%AC&to=%E4%B8%8A%E6%B5%B7&date=2024-01-01&passengers=2
};

// ===== 实际应用场景 =====

// 场景1: 页面初始化时获取参数
export const initPageWithParams = () => {
  const params = getUrlParams();
  
  if (params.id) {
    // 根据ID加载数据
    console.log('加载ID为', params.id, '的数据');
  }
  
  if (params.type) {
    // 根据类型设置页面状态
    console.log('页面类型:', params.type);
  }
};

// 场景2: 页面跳转时传递参数
export const navigateWithParams = () => {
  const targetUrl = buildUrlWithParams('/pages/result/index', {
    searchType: 'flight',
    from: '北京',
    to: '上海',
    date: '2024-01-01'
  });
  
  // 使用Taro跳转
  // Taro.navigateTo({ url: targetUrl });
  console.log('跳转URL:', targetUrl);
};

// 场景3: 处理中文参数
export const handleChineseParams = () => {
  // 中文参数会自动进行URL编码和解码
  const params = {
    city: '北京市',
    district: '朝阳区',
    keyword: '首都机场'
  };
  
  const url = buildUrlWithParams('/pages/search/index', params);
  console.log('包含中文的URL:', url);
  
  // 解析时会自动解码
  const parsedParams = getUrlParams(url);
  console.log('解析后的参数:', parsedParams);
  // 输出: { city: '北京市', district: '朝阳区', keyword: '首都机场' }
};

// 场景4: 参数验证和默认值处理
export const handleParamsWithDefaults = () => {
  const id = getUrlParam('id') || 'default-id';
  const page = parseInt(getUrlParam('page') || '1');
  const size = parseInt(getUrlParam('size') || '10');
  
  console.log('处理后的参数:', { id, page, size });
};

// 场景5: 动态更新URL参数（用于分享等场景）
export const updateUrlParams = () => {
  const currentParams = getUrlParams();
  const newParams = {
    ...currentParams,
    timestamp: Date.now(),
    version: '1.0'
  };
  
  const currentUrl = getCurrentPageUrl().split('?')[0]; // 获取不带参数的URL
  const newUrl = buildUrlWithParams(currentUrl, newParams);
  
  console.log('更新后的URL:', newUrl);
};
