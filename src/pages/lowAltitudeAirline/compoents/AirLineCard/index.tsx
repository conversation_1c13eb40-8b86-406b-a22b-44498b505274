import { memo } from 'react';
import { Image, View } from '@tarojs/components';
import { Edit, User } from '@nutui/icons-react-taro';
import { flightLine } from '@/utils/img';
import './index.less';
import CustomTag from '@/components/CustomTag';

interface AirLineCardProps {
  type?: 'go' | 'back' | 'order' | 'orderDetail'; //去程｜返程｜订单|订单详情
}
const AirLineCard = ({ type = 'go' }: AirLineCardProps) => {
  return (
    <>
      <View className='airline-card'>
        <View className={'time-box'}>
          <View className={'time-info'}>
            {type === 'go' && <CustomTag type={'blue'}>去程</CustomTag>}
            {type === 'back' && <CustomTag type={'blue'}>返程</CustomTag>}

            <CustomTag type={'default'}>
              4/17 周四{' '}
              {(type === 'go' || type === 'back') && <Edit width={14} className={'ml-4'} />}
            </CustomTag>
            <CustomTag type={'default'}>
              <User color={'#4F5170'} width={12} height={12} className={'mr-4'} />
              5人
            </CustomTag>
          </View>
          {type === 'order' && (
            <>
              <View className={'price'}>¥ 1070</View>
            </>
          )}
        </View>
        <View className={'airline-info'}>
          <View className={'left'}>
            <View className={'city-name'}>成都</View>
            <View className={'airport-name'}>不限机场</View>
          </View>
          <View className={'center'}>
            <View className={'flight-type'}>空客H135</View>
            <View className={'line'}>
              <Image src={flightLine} className={'split-line-img'} />
            </View>
            <View className={'duration-time'}>1h50m</View>
          </View>
          <View className={'right'}>
            <View className={'city-name'}>成都</View>
            <View className={'airport-name'}>不限机场</View>
          </View>
        </View>
      </View>
    </>
  );
};
export default memo(AirLineCard);
