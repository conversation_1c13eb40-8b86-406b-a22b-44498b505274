import { memo, useState } from 'react';
import { Button, Image, Input, ScrollView, Text, View } from '@tarojs/components';
import './index.less';
import CustomHeader from '@/components/CustomHeader';
import { Clock } from '@nutui/icons-react-taro';
import { detailDescription } from '@/utils/img';
import { DatePicker, InputNumber, Range } from '@nutui/nutui-react-taro';
import BottomCtr from '@/components/BottomCtr';
import AirLineCard from '@/pages/lowAltitudeAirline/compoents/AirLineCard';
import CustomTag from '@/components/CustomTag';

const REMARK_LIST = [
  '有医疗需求',
  '有80岁以上老人',
  '有2岁以下婴儿',
  '有宠物',
  '有高尔夫球包',
  '其他',
];

const LowAirLineDetail = () => {
  const [formData, setFormData] = useState<any>({});
  const [hourSelectorShow, setHourSelectorShow] = useState(false);
  return (
    <>
      <View className={'low-airline-detail'}>
        <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
          <CustomHeader showBackButton={true} bgColor={'transparent'} title={'低空航线预定'} />
          <View className={'low-airline-detail-content'}>
            <View className={'airline-box card-box'}>
              <AirLineCard />
              <View className={'passenger-num border-box'}>
                <View className={'label-txt'}>
                  <Text>*</Text> 预计出行人数
                </View>
                <View className={'num-box'}>
                  <InputNumber
                    className='num-input'
                    value={formData?.passengerNum || 10}
                    min={1}
                    max={20}
                    step={1}
                    formatter={value => `${value}人出行`}
                    onChange={value => {
                      setFormData({ ...formData, passengerNum: value });
                    }}
                  />
                </View>
                <Range
                  className='num-range'
                  value={formData?.passengerNum || 10}
                  min={0}
                  max={20}
                  step={1}
                  onChange={value => {
                    setFormData({ ...formData, passengerNum: value });
                  }}
                  marks={{
                    1: 1,
                    5: 5,
                    10: 10,
                    15: 15,
                    20: 20,
                  }}
                />
              </View>
              <View className={'hour-box border-box'}>
                <View className={'label-txt'}>
                  <Text>*</Text> 具体出行时间
                </View>
                <View
                  className={'form-item'}
                  onClick={() => setHourSelectorShow(!hourSelectorShow)}
                >
                  <Text className={'form-label'}>具体时间</Text>
                  <View className={`time-hour input-box ${formData?.hour ? 'active' : ''}`}>
                    <Clock width={14} className={'clock-icon'} />
                    {formData?.hour?.join(':') || '请选择时间'}
                  </View>
                  {hourSelectorShow && (
                    <DatePicker
                      title='时间选择'
                      type='hour-minutes'
                      visible={hourSelectorShow}
                      value={new Date(formData?.date)}
                      onConfirm={(_options, values) => {
                        console.log('onConfirm', values);
                        setFormData({ ...formData, hour: values });
                        setHourSelectorShow(false);
                      }}
                      onCancel={() => setHourSelectorShow(false)}
                    />
                  )}
                </View>
              </View>
            </View>
            <View className={'card-box mt-12'}>
              <View className={'label-txt'}>
                <Text>*</Text>联系人信息
              </View>
              <View className={'form-item'}>
                <View className={'form-label'}>姓名</View>
                <View className={'input-box'}>
                  <Input
                    placeholder='请输入'
                    placeholderClass={'placeholder'}
                    value={formData?.name}
                    onInput={e => setFormData({ ...formData, name: e.detail.value })}
                  />
                </View>
              </View>
              <View className={'form-item'}>
                <View className={'form-label'}>联系方式</View>
                <View className={'input-box'}>
                  <Input
                    placeholder='请输入'
                    placeholderClass={'placeholder'}
                    value={formData?.phone}
                    onInput={e => setFormData({ ...formData, phone: e.detail.value })}
                  />
                </View>
              </View>
            </View>
            <View className={'card-box mt-12'}>
              <View className={'label-txt'}>备注信息</View>
              <View className={'tag-list'}>
                {REMARK_LIST.map(item => {
                  return (
                    <CustomTag
                      type={formData?.remark?.includes(item) ? 'blue' : 'gray'}
                      size={'medium'}
                      key={item}
                      onClick={() => {
                        setFormData({
                          ...formData,
                          remark: `${formData?.remark ? formData?.remark + ',' : ''}${item}`,
                        });
                        console.log(`${formData?.remark ? formData?.remark + ',' : ''}${item}`);
                      }}
                    >
                      {item}
                    </CustomTag>
                  );
                })}
              </View>
              <View className={'form-item'}>
                <View className={'input-box'}>
                  <Input
                    placeholder='请输入备注信息'
                    placeholderClass={'placeholder'}
                    value={formData?.remarkInput}
                    onInput={e => setFormData({ ...formData, remarkInput: e.detail.value })}
                  />
                </View>
              </View>
            </View>
            <View className={'mt-12 detail-description'}>
              <View className={'title mb-12'}>图片介绍</View>
              <Image src={detailDescription} />
            </View>
          </View>
        </ScrollView>
        <BottomCtr>
          <Button className={'pay-btn'} type={'primary'}>
            立即支付
          </Button>
        </BottomCtr>
      </View>
    </>
  );
};
export default memo(LowAirLineDetail);
