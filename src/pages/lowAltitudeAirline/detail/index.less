@color-blue: #124072;
.low-airline-detail {
  width: 100%;
  min-height: 100vh;
  //padding: 0 16px;
  box-sizing: border-box;
  background: linear-gradient(180deg, @color-blue 11%, rgba(18, 64, 114, 0) 22%, #f0f4fa 100%);
  .low-airline-detail-content {
    padding: 16px;
    box-sizing: border-box;
  }
  .airline-box {
    .num-box {
      text-align: center;
      margin-bottom: 20px;
    }
  }
  .passenger-num {
    margin-bottom: 16px;
    .num-input {
      --nutui-inputnumber-input-width: 100px;
      --nutui-inputnumber-input-height: 28px;
      --nutui-inputnumber-input-border: 1px solid #e7e8e9;
      --nutui-inputnumber-input-background-color: #fff;
      --nutui-inputnumber-button-background-color: #f5f6fa;
    }
    .num-range {
      height: 50px;
      --nutui-range-inactive-color: #edeff5;
      --nutui-range-active-color: @color-blue;
      --nutui-range-color: #737578;
    }
  }
  .hour-box {
  }

  .label-txt {
    color: #404245;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;
    text {
      color: #f83116;
      font-size: 12px;
      padding-right: 2px;
    }
  }
  .form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    gap: 8px;
    .form-label {
      color: #4f5170;
      font-size: 14px;
      white-space: nowrap;
      text-align: left;
      width: 80px;
    }
    .input-box {
      width: 100%;
      height: 40px;
      border-radius: 8px;
      background: #f2f4fa;
      box-sizing: border-box;
      padding: 2px 10px;
      display: inline-flex;
      align-items: center;
      color: #9aa2ca;
      &.active {
        color: #23242d;
      }
      .clock-icon {
        margin-right: 6px;
      }
    }
    input {
      width: 100%;
      height: 100%;
      border: none;
      outline: none;
      font-size: 14px;
      color: #23242d;
      padding: 0;
      background: none;
      box-sizing: border-box;
      text-align: left;
    }
  }
  .detail-description {
    .title {
      font-size: 20px;
      color: @color-blue;
    }
    image {
      width: 100%;
      height: 1495px;
      //height: 100%;
    }
  }
  .pay-btn {
    background: @color-blue;
    border-radius: 12px;
  }
}
