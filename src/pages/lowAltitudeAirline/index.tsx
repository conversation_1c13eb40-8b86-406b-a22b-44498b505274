import { memo } from 'react';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import './index.less';
import { flight, hots } from '@/utils/img';
import SearchAirline from '@/components/SearchAirline';
import CustomTab from '@/components/CustomTab';
import { toUrl } from '@/utils';
import BatterySafetyNotice from '@/components/BatterySafetyNotice';
import BuyTicketNotice from '@/components/BuyTicketNotice';
import RecommendList from '@/components/RecommendList';

const LowAltitudeAirline = () => {
  return (
    <>
      <View className='low-airline'>
        <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
          <CustomHeader showBackButton={true} bgColor={'transparent'} title={'低空航线'} />
          <CustomTab
            className={'tabs'}
            list={[
              {
                label: '单程',
                value: 'one-way',
                children: (
                  <SearchAirline
                    type={'oneWay'}
                    getData={() => {
                      toUrl('/pages/lowAltitudeAirline/detail/index');
                    }}
                  />
                ),
              },
              {
                label: '往返',
                value: 'round-trip',
                children: (
                  <SearchAirline
                    type={'roundTrip'}
                    getData={() => {
                      toUrl('/pages/lowAltitudeAirline/detail/index');
                    }}
                  />
                ),
              },
              {
                label: '多程',
                value: 'multipass',
                children: (
                  <SearchAirline
                    type={'multipass'}
                    getData={() => {
                      toUrl('/pages/lowAltitudeAirline/detail/index');
                    }}
                  />
                ),
              },
            ]}
            defaultValue={'one-way'}
          />
          <View className={'tips-box'}>
            <BatterySafetyNotice />
            <BuyTicketNotice />
          </View>
          <View className={'hot-recommend'}>
            <View className={'title'}>
              <Image src={hots} />
              <Text>热门低空航线</Text>
            </View>
            <View className={'flight-bg'}>
              <Image src={flight} />
            </View>
            <RecommendList />
          </View>
        </ScrollView>
      </View>
    </>
  );
};
export default memo(LowAltitudeAirline);
