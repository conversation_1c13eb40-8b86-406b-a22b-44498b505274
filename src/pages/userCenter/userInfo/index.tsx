import { memo, useState } from "react";
import CustomHeader from "@/components/CustomHeader";
import { View, Button, Input, Picker, Text } from "@tarojs/components";
import "./index.less";
import Modal from "@/components/Modal";
import { logo } from "@/utils/img";
import { Cell, CellGroup, Image as ImageNutUI } from "@nutui/nutui-react-taro";
import { ArrowRight } from "@nutui/icons-react-taro";

const ArrowRightIcon = (
  <ArrowRight color={"#B5BACA"} width={14} style={{ marginLeft: 6 }} />
);

const UserInfo = () => {
  const [showInputModal, setShowInputModal] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [showProtocolModal, setProtocolModal] = useState(false);

  return (
    <>
      <View className="user-info">
        <CustomHeader
          showBackButton={true}
          backIconColor={"#2B2B32"}
          bgColor={"transparent"}
          title={"个人信息"}
          titleColor={"#1D1F20"}
        />
        <View className={"user-info-container"}>
          <View className="card-box">
            <CellGroup>
              <Cell
                title="头像"
                extra={
                  <>
                    <ImageNutUI
                      src={logo}
                      width={"40"}
                      height={"40"}
                      radius={40}
                    />
                    {ArrowRightIcon}
                  </>
                }
                onClick={() => setShowInputModal("modifyName")}
              />
              <Picker
                mode="selector"
                range={["男", "女", "保密"]}
                onChange={() => {}}
              >
                <Cell
                  title="性别"
                  extra={
                    <>
                      <Text>女</Text>
                      {ArrowRightIcon}
                    </>
                  }
                />
              </Picker>

              <Cell
                title="关联手机"
                extra={
                  <>
                    <Text>13212341234</Text>
                    {ArrowRightIcon}
                  </>
                }
                onClick={() => setShowInputModal("modifyPhone")}
              />
              <Cell
                title="协议与说明"
                extra={
                  <>
                    <Text>详情</Text>
                    {ArrowRightIcon}
                  </>
                }
                onClick={() => setProtocolModal(true)}
              />
            </CellGroup>
          </View>
          <Button className="user-info-btn primary-btn" type="primary">
            退出登录
          </Button>
          <Button className="user-info-btn normal-btn">注销</Button>
        </View>
      </View>
      {/*修改手机号/用户名*/}
      <Modal
        show={!!showInputModal}
        title={showInputModal === "modifyPhone" ? "修改用户名" : "修改手机号"}
        showCloseBtn={false}
      >
        <View className={"user-info-modal-body"}>
          <Input
            placeholder={
              showInputModal === "modifyPhone" ? "请输入手机号" : "请输入用户名"
            }
            value={inputValue}
            type={showInputModal === "modifyPhone" ? "number" : "text"}
            onInput={(e) => {
              setInputValue(e.detail.value);
            }}
          />
          <View className="modal-btn-box">
            <Button
              className="modal-btn cancel-btn"
              onClick={() => setShowInputModal("")}
            >
              取消
            </Button>
            <Button
              className="modal-btn sure-btn"
              type={"primary"}
              onClick={() => setShowInputModal("")}
            >
              确定
            </Button>
          </View>
        </View>
      </Modal>
      <Modal
        show={showProtocolModal}
        title={"用户协议"}
        onClose={() => {
          setProtocolModal(false);
        }}
      >
        用户协议内容
      </Modal>
    </>
  );
};
export default memo(UserInfo);
