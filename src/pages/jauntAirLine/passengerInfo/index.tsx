import { memo, useState, useEffect } from 'react';
import { View, Text, ScrollView } from '@tarojs/components';
import { Button, Checkbox, Collapse } from '@nutui/nutui-react-taro';
import CustomHeader from '@/components/CustomHeader';
import { toUrl, getUrlParam, _localStorage, formatDate } from '@/utils';
import './index.less';
import '../selectCabin/index.less';
import { ArrowDown, ArrowRight, Plus } from '@nutui/icons-react-taro';
import BottomCtr from '@/components/BottomCtr';
import CustomTag from '@/components/CustomTag';
import FlightItem from '@/pages/jauntAirLine/components/FlightItem';
import { CommonPassengerListVO, FlightPlanBO, FlightCabinBO } from '@/api/servies/dkx';
import api from '@/api';
import { SUCCESS_CODE } from '@/utils/constant';

interface PassengerInfo extends CommonPassengerListVO {
  selected?: boolean;
}

interface CabinData extends FlightCabinBO {
  flightInfo?: FlightPlanBO;
}

const PassengerInfo = () => {
  // 航班数据和舱位数据
  const [flightInfo, setFlightInfo] = useState<FlightPlanBO>();
  const [cabinData, setCabinData] = useState<CabinData>();

  // 乘机人列表
  const [passengers, setPassengers] = useState<PassengerInfo[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取数据
  useEffect(() => {
    // 获取航班数据
    const flightDataStr = _localStorage.getItem('flightData');
    if (flightDataStr) {
      const parsedFlightData = JSON.parse(flightDataStr);
      setFlightInfo(parsedFlightData);
    }

    // 获取舱位数据
    const cabinDataStr = getUrlParam('cabinData');
    if (cabinDataStr) {
      const parsedCabinData: CabinData = JSON.parse(cabinDataStr as string);
      setCabinData(parsedCabinData);
    }

    // 获取乘机人列表
    fetchPassengerList();
  }, []);

  // 获取乘机人列表
  const fetchPassengerList = async () => {
    try {
      setLoading(true);
      const { code, data } = await api.v10PassengerListCreate();
      if (code === SUCCESS_CODE && data) {
        setPassengers(data.map(item => ({ ...item, selected: false })));
      }
    } catch (error) {
      console.error('获取乘机人列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取证件类型显示名称
  const getCertTypeName = (certType?: number) => {
    const typeMap: { [key: number]: string } = {
      1: '身份证',
      2: '护照',
      3: '户口簿',
      4: '出生证明',
      5: '其他',
    };
    return typeMap[certType || 1] || '身份证';
  };

  // 脱敏显示证件号
  const maskCertNo = (certNo?: string) => {
    if (!certNo) return '';
    if (certNo.length <= 8) return certNo;
    return certNo.substring(0, 4) + '****' + certNo.substring(certNo.length - 4);
  };

  // 联系人信息
  const [contactInfo, setContactInfo] = useState({
    name: '安小妮',
    phone: '173*******3102',
  });

  // 保险选择 - 暂时注释
  // const [selectedInsurance, setSelectedInsurance] = useState('保障险');

  // 协议勾选
  const [agreementChecked, setAgreementChecked] = useState(true);

  // 总价
  const [totalPrice, setTotalPrice] = useState(870);

  // 优惠金额
  const [discountAmount, setDiscountAmount] = useState(80);

  // 选择乘机人
  const togglePassengerSelection = (id?: number) => {
    if (!id) return;
    setPassengers(passengers.map(p => (p.id === id ? { ...p, selected: !p.selected } : p)));
  };

  // 添加乘机人
  const handleAddPassenger = () => {
    // 跳转到添加乘机人页面
    toUrl('/pages/jauntAirLine/addPassenger/index');
  };

  // 编辑乘机人信息
  const editPassenger = (passenger: PassengerInfo) => {
    // 跳转到编辑乘机人页面
    toUrl(`/pages/jauntAirLine/addPassenger/index?passengerId=${passenger.id}&isEdit=true`);
  };

  // 删除乘机人
  const deletePassenger = async (id?: number) => {
    if (!id) return;
    try {
      const { code } = await api.v10PassengerDeleteCreate({ id: id.toString() });
      if (code === SUCCESS_CODE) {
        // 重新获取乘机人列表
        fetchPassengerList();
      }
    } catch (error) {
      console.error('删除乘机人失败:', error);
    }
  };

  // 编辑联系人信息
  const editContactInfo = () => {
    console.log('编辑联系人信息');
    // 这里可以跳转到编辑页面或者弹出编辑弹窗
  };

  // 表单验证
  const isFormValid = () => {
    // 至少选择一个乘机人
    const hasSelectedPassenger = passengers.some(p => p.selected);

    // 联系人信息完整
    const hasContactInfo = contactInfo.name && contactInfo.phone;

    // 同意协议
    return hasSelectedPassenger && hasContactInfo && agreementChecked;
  };

  // 提交订单
  const handleSubmitOrder = () => {
    if (isFormValid()) {
      toUrl('/pages/jauntAirLine/payment/index');
    }
  };

  return (
    <View className='select-cabin-page passenger-info-page '>
      <CustomHeader showBackButton={true} bgColor={'transparent'} title={'旅客信息'} />
      <View className='card-box flight-info-card'>
        <Collapse expandIcon={<ArrowDown />}>
          <Collapse.Item
            title={
              <View className={'flight-info-title'}>
                <CustomTag type={'gray'}>出发</CustomTag>
                <Text>{formatDate(flightInfo?.flightDate)}</Text>

                <View className={'split-line'} />
                <View>
                  {flightInfo?.departCity} - {flightInfo?.arriveCity}
                </View>
              </View>
            }
          >
            <FlightItem flight={flightInfo} />
          </Collapse.Item>
        </Collapse>
      </View>
      <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
        {/* 乘机人选择 */}
        <View className='card-box passenger-section'>
          <View className='section-title'>乘机人</View>
          <View className='passenger-selector'>
            {loading ? (
              <Text>加载中...</Text>
            ) : (
              passengers.map(passenger => (
                <View
                  key={passenger.id}
                  className={`passenger-tag ${passenger.selected ? 'selected' : ''}`}
                  onClick={() => togglePassengerSelection(passenger.id)}
                >
                  <Text>{passenger.name || passenger.firstName + passenger.nextName}</Text>
                </View>
              ))
            )}
            <View className='add-passenger-btn' onClick={handleAddPassenger}>
              <Plus color='#1663F8' size={16} />
              <Text>新增乘机人</Text>
            </View>
          </View>

          {/* 已选乘机人信息 */}
          {passengers
            .filter(p => p.selected)
            .map(passenger => (
              <View
                key={passenger.id}
                className='passenger-info-item'
                onClick={() => editPassenger(passenger)}
              >
                <View className='passenger-info-left'>
                  <Text className='passenger-name'>
                    {passenger.name || passenger.firstName + passenger.nextName}
                  </Text>
                  <Text className='passenger-count'>成人</Text>
                </View>
                <View className='passenger-info-right'>
                  <Text className='id-info'>
                    {getCertTypeName(passenger.certType)}：{maskCertNo(passenger.certNo)}
                  </Text>
                  <ArrowRight color='#999' size={16} />
                </View>
              </View>
            ))}
        </View>

        {/* 联系电话 */}
        <View className='card-box contact-phone-section'>
          <Text className='contact-phone-label'>联系电话</Text>
          <Text className='contact-phone-value'>+86 {contactInfo.phone}</Text>
        </View>

        {/* 联系人信息 */}
        <View className='card-box contact-info-section'>
          <View className='section-title'>联系人信息</View>
          <Text className='section-subtitle'>用于接收航班信息</Text>

          <View className='contact-info-item' onClick={editContactInfo}>
            <View className='contact-info-left'>
              <Text className='contact-label'>姓名</Text>
              <Text className='contact-value'>{contactInfo.name}</Text>
            </View>
            <View className='edit-icon'>
              <ArrowRight color='#999' size={16} />
            </View>
          </View>

          <View className='contact-info-item' onClick={editContactInfo}>
            <View className='contact-info-left'>
              <Text className='contact-label'>联系电话</Text>
              <Text className='contact-value'>+86 {contactInfo.phone}</Text>
            </View>
            <View className='edit-icon'>
              <ArrowRight color='#999' size={16} />
            </View>
          </View>
        </View>

        {/* 航空保险 - 暂时注释 */}
        {/* <View className='card-box insurance-section'>
          <View className='section-title'>航空保险</View>
          <Text className='section-subtitle'>出行有保障，人人更安心</Text>
          ...
        </View> */}

        {/* 协议勾选 */}
        <View className='agreement-section'>
          <Checkbox checked={agreementChecked} onChange={val => setAgreementChecked(val)}>
            <Text className='agreement-text'>我已确认乘客姓名，且认真阅读并同意</Text>
          </Checkbox>
          <View className='agreement-links'>
            <Text className='agreement-link'>《中国内地旅客须知》</Text>
            <Text className='agreement-link'>《东航无障碍服务》</Text>
            <Text className='agreement-link'>《东航旅客及行李运输规定》</Text>
            <Text className='agreement-link'>《购票须知》</Text>
            <Text className='agreement-link'>《特殊旅客须知》</Text>
          </View>
        </View>

        {/* 卡券优惠 */}
      </ScrollView>

      {/* 底部操作栏 */}
      <BottomCtr>
        <View className='discount-section' onClick={() => console.log('选择优惠券')}>
          <Text className='discount-label'>卡券优惠</Text>
          <View className='discount-value'>
            <Text className='discount-amount'>-¥{discountAmount}</Text>
            <ArrowRight color='#999' size={16} />
          </View>
        </View>
        <View className='bottom-action'>
          <View className='price-info'>
            <Text className='price-label'>¥</Text>
            <Text className='price-value'>{cabinData?.price || 0}</Text>
            <Text className='price-detail'>明细</Text>
          </View>
          <Button
            className={`submit-btn ${!isFormValid() ? 'disabled' : ''}`}
            disabled={!isFormValid()}
            onClick={handleSubmitOrder}
          >
            提交订单
          </Button>
        </View>
      </BottomCtr>
    </View>
  );
};

export default memo(PassengerInfo);
