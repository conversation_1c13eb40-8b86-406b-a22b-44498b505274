import { memo, useState } from 'react';
import { View, Text, ScrollView, Image } from '@tarojs/components';
import { Button } from '@nutui/nutui-react-taro';
import CustomHeader from '@/components/CustomHeader';
import { wx } from '@/utils/img';
import { toUrl } from '@/utils';
import './index.less';
import '../selectCabin/index.less';
import { ArrowRight } from '@nutui/icons-react-taro';
import FlightItem from '../components/FlightItem';
import BottomCtr from '@/components/BottomCtr';

const Payment = () => {
  // 订单信息
  const [orderInfo] = useState({
    orderNo: '12345456712631',
    createTime: '2024-04-20 14:20',
    timeLimit: 30, // 支付时间限制（分钟）
    totalPrice: 1070,
  });

  // 航班信息
  const [flightInfo] = useState({
    id: '1',
    flightNo: '3U6711',
    departureCity: '成都',
    arrivalCity: '绵阳',
    departureAirport: '双流',
    arrivalAirport: '南郊',
    departureTerminal: '',
    arrivalTerminal: '',
    departureTime: '10:40',
    arrivalTime: '13:30',
    duration: '3h50m',
    price: 1070,
    aircraft: '空客H135',
    airline: '川航',
    isDirect: true,
    date: '6月30日',
  });

  // 乘机人信息
  const [passengers] = useState([
    {
      id: '1',
      name: '安妮',
      type: '成人',
      idType: 'ID_CARD',
      idNumber: '5139**********048',
    },
    {
      id: '2',
      name: '安小妮',
      type: '儿童',
      idType: 'ID_CARD',
      idNumber: '5139**********048',
    },
  ]);

  // 立即支付
  const handlePay = () => {
    // 模拟支付成功后跳转到订单详情页
    toUrl('/pages/orders/lowAirLineOrder/detail/index');
  };

  // 取消订单
  const handleCancelOrder = () => {
    console.log('取消订单');
  };

  return (
    <View className='select-cabin-page payment-page'>
      <CustomHeader showBackButton={true} bgColor={'transparent'} title={'订单支付'} />

      <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
        {/* 支付倒计时提示 */}
        <View className='card-box payment-timer'>
          <Text className='timer-text'>请在{orderInfo.timeLimit}分钟内尽快完成支付</Text>
          <View className='order-info'>
            <Text className='order-label'>订单号：</Text>
            <Text className='order-value'>{orderInfo.orderNo}</Text>
          </View>
          <View className='order-info'>
            <Text className='order-label'>截止时间：</Text>
            <Text className='order-value'>{orderInfo.createTime}</Text>
          </View>
        </View>

        {/* 支付方式 */}
        <View className='card-box payment-method'>
          <View className='section-title'>支付方式</View>
          <View className='method-item selected'>
            <View className='method-left'>
              <Image src={wx} className='method-icon' />
              <Text className='method-name'>微信支付</Text>
            </View>
            <View className='method-right'>
              <View className='check-icon'></View>
            </View>
          </View>
        </View>

        {/* 航班信息 */}
        <View className='card-box flight-info-section'>
          <View className='section-title'>航班信息</View>
          <View className='flight-card'>
            <View className='flight-header'>
              <Text className='flight-date'>{flightInfo.date}</Text>
            </View>
            <FlightItem flight={flightInfo} />
          </View>
        </View>

        {/* 支付信息 */}
        <View className='card-box payment-info'>
          <View className='section-title'>支付信息</View>
          <View className='price-item' onClick={() => console.log('查看价格明细')}>
            <Text className='price-label'>实际支付</Text>
            <View className='price-value'>
              <Text className='price'>{orderInfo.totalPrice}</Text>
              <Text className='detail'>
                明细 <ArrowRight size={12} color='#999' />
              </Text>
            </View>
          </View>
        </View>

        {/* 乘机人信息 */}
        <View className='card-box passenger-info-section'>
          <View className='section-title'>乘机人信息</View>
          {passengers.map(passenger => (
            <View key={passenger.id} className='passenger-item'>
              <View className='passenger-left'>
                <Text className='passenger-name'>{passenger.name}</Text>
                <View className='passenger-tag'>{passenger.type}</View>
              </View>
              <View className='passenger-right'>
                <Text className='id-info'>身份证：{passenger.idNumber}</Text>
                <ArrowRight size={16} color='#999' />
              </View>
            </View>
          ))}
        </View>

        {/* 取消订单按钮 */}
        <View className='cancel-order' onClick={handleCancelOrder}>
          <Text>取消订单</Text>
        </View>
      </ScrollView>

      {/* 底部支付按钮 */}
      <BottomCtr>
        <View className='bottom-action'>
          <View className='price-info'>
            <Text className='price-symbol'>¥</Text>
            <Text className='price-value'>{orderInfo.totalPrice}</Text>
          </View>
          <Button className='pay-btn' onClick={handlePay}>
            立即支付
          </Button>
        </View>
      </BottomCtr>
    </View>
  );
};

export default memo(Payment);
