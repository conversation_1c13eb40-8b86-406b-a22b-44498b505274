@import '../../../styles/variables.less';

.add-passenger-page {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, rgba(22, 99, 248, 0.08) 0%, #f7f8fa 20%);
  padding-bottom: 120px; // 为底部按钮留出空间

  .form-container {
    margin: 16px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    overflow: hidden;

    .form-item {
      position: relative;
      padding: 20px 16px;
      border-bottom: 1px solid #f5f6fa;
      transition: background-color 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: #f8f9fa;
      }

      .label {
        font-size: 15px;
        font-weight: 500;
        color: @color-text-primary;
        margin-bottom: 12px;
        display: block;

        &::after {
          content: '*';
          color: #ff4757;
          margin-left: 4px;
        }
      }

      // 身份证类型不需要必填标识的字段
      &.optional .label::after {
        display: none;
      }

      .input {
        width: 100%;
        height: 44px;
        padding: 0 12px;
        border: 1px solid #e8eaed;
        border-radius: 8px;
        font-size: 15px;
        color: @color-text-primary;
        background: #fafbfc;
        transition: all 0.2s ease;
        box-sizing: border-box;

        &:focus {
          border-color: @color-primary;
          background: #fff;
          box-shadow: 0 0 0 3px rgba(22, 99, 248, 0.1);
        }

        &::placeholder {
          color: #9aa0a6;
          font-size: 14px;
        }
      }

      .picker-value {
        width: 100%;
        height: 44px;
        padding: 0 12px;
        border: 1px solid #e8eaed;
        border-radius: 8px;
        background: #fafbfc;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 15px;
        color: @color-text-primary;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;

        &:active {
          border-color: @color-primary;
          background: #fff;
        }

        &::after {
          content: '';
          width: 0;
          height: 0;
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
          border-top: 6px solid #9aa0a6;
          margin-left: 8px;
        }

        &.placeholder {
          color: #9aa0a6;
        }
      }

      .radio-group {
        display: flex;
        gap: 24px;
        margin-top: 8px;

        .radio-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          border-radius: 20px;
          border: 1px solid #e8eaed;
          background: #fafbfc;
          cursor: pointer;
          transition: all 0.2s ease;
          flex: 1;
          justify-content: center;

          &:active {
            transform: scale(0.98);
          }

          .radio-label {
            font-size: 14px;
            color: @color-text-primary;
            font-weight: 500;
          }

          // 选中状态
          &.selected {
            border-color: @color-primary;
            background: rgba(22, 99, 248, 0.08);

            .radio-label {
              color: @color-primary;
            }
          }
        }
      }
    }

    // 表单分组
    .form-section {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        padding: 16px;
        font-size: 16px;
        font-weight: 600;
        color: @color-text-primary;
        background: #f8f9fa;
        border-bottom: 1px solid #f0f1f3;
      }
    }
  }

  // 底部按钮容器
  .bottom-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 16px;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
    z-index: 100;

    // 适配安全区域
    padding-bottom: calc(16px + env(safe-area-inset-bottom));

    .submit-btn {
      width: 100%;
      height: 48px;
      background: linear-gradient(135deg, @color-primary 0%, #4285f4 100%);
      border-radius: 24px;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      border: none;
      box-shadow: 0 4px 12px rgba(22, 99, 248, 0.3);
      transition: all 0.2s ease;

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 8px rgba(22, 99, 248, 0.3);
      }

      &:disabled {
        background: #d1d5db;
        box-shadow: none;
        transform: none;
      }

      // 加载状态
      &.loading {
        background: #9ca3af;
        cursor: not-allowed;
      }
    }
  }

  // 响应式适配
  @media (max-width: 375px) {
    .form-container {
      margin: 12px;

      .form-item {
        padding: 16px 12px;

        .input,
        .picker-value {
          height: 40px;
          font-size: 14px;
        }

        .radio-group {
          gap: 16px;

          .radio-item {
            padding: 6px 12px;

            .radio-label {
              font-size: 13px;
            }
          }
        }
      }
    }

    .bottom-container {
      padding: 12px;
      padding-bottom: calc(12px + env(safe-area-inset-bottom));

      .submit-btn {
        height: 44px;
        font-size: 15px;
      }
    }
  }
}
