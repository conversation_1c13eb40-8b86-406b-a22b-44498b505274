.add-passenger-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-size: 14px;

  .form-container {
    padding: 32px;
    background-color: #fff;
    margin: 32px;
    border-radius: 16px;

    .form-item {
      display: flex;
      align-items: center;
      padding: 32px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .label {
        width: 200px;
        color: #333;
        flex-shrink: 0;
      }

      .input {
        flex: 1;
        color: #333;
        text-align: right;

        &::placeholder {
          color: #999;
        }
      }

      .picker-value {
        flex: 1;
        color: #333;
        text-align: right;
        padding: 16px 0;
      }

      .radio-group {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        gap: 32px;

        .radio-item {
          display: flex;
          align-items: center;
          gap: 16px;

          .radio-label {
            color: #333;
          }
        }
      }
    }
  }

  .submit-btn {
    width: 100%;
    height: 36px;
    background: linear-gradient(135deg, #1663f8 0%, #4285f4 100%);
    border-radius: 48px;
    color: #fff;
    font-weight: 500;
    border: none;

    &:disabled {
      background: #ccc;
    }
  }
}
