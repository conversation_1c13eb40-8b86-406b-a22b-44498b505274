import { memo, useState, useEffect } from 'react';
import { View, Text, Input } from '@tarojs/components';
import { <PERSON><PERSON>, Picker, Radio, DatePicker, Toast } from '@nutui/nutui-react-taro';
import CustomHeader from '@/components/CustomHeader';
import { toUrl, getUrlParam } from '@/utils';
import './index.less';
import { AddCommonPassengerDTO, UpdateCommonPassengerDTO, NationalityVO } from '@/api/servies/dkx';
import api from '@/api';
import { SUCCESS_CODE } from '@/utils/constant';
import dayjs from 'dayjs';
import BottomCtr from '@/components/BottomCtr';

const AddPassenger = () => {
  const [isEdit, setIsEdit] = useState(false);
  const [passengerId, setPassengerId] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // 表单数据
  const [formData, setFormData] = useState<AddCommonPassengerDTO>({
    name: '',
    firstName: '',
    nextName: '',
    mobile: '',
    sex: '1', // 1男 2女
    certType: 1, // 1身份证 2护照
    certNo: '',
    certValidity: '',
    issuerCountry: 'CN',
    nationality: 'CN',
    birthday: '',
  });

  // 证件类型选项
  const certTypeOptions = [
    { text: '身份证', value: 1 },
    { text: '出生证明', value: 2 },
  ];

  // 性别选项
  const genderOptions = [
    { label: '男', value: '1' },
    { label: '女', value: '2' },
  ];

  useEffect(() => {
    // 检查是否为编辑模式
    const editMode = getUrlParam('isEdit');
    const id = getUrlParam('passengerId');

    if (editMode === 'true' && id) {
      setIsEdit(true);
      setPassengerId(id as string);
    }
  }, []);

  // 更新表单数据
  const updateFormData = (key: keyof AddCommonPassengerDTO, value: any) => {
    setFormData(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // 表单验证
  const validateForm = () => {
    if (formData.certType === 1) {
      // 身份证
      if (!formData.name) {
        console.log('请输入姓名');
        return false;
      }
    } else {
      // 其他证件
      if (!formData.firstName || !formData.nextName) {
        console.log('请输入姓和名');
        return false;
      }
      if (!formData.birthday) {
        console.log('请选择出生日期');
        return false;
      }
      if (!formData.certValidity) {
        console.log('请选择证件有效期');
        return false;
      }
    }

    if (!formData.certNo) {
      console.log('请输入证件号码');
      return false;
    }

    if (!formData.mobile) {
      console.log('请输入手机号');
      return false;
    }

    return true;
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      if (isEdit) {
        // 编辑模式
        const updateData: UpdateCommonPassengerDTO = {
          id: Number(passengerId),
          ...formData,
        };
        const { code } = await api.v10PassengerUpdateCreate(updateData);
        if (code === SUCCESS_CODE) {
          console.log('更新成功');
          toUrl('/pages/jauntAirLine/passengerInfo/index');
        }
      } else {
        // 新增模式
        const { code } = await api.v10PassengerAddCreate(formData);
        if (code === SUCCESS_CODE) {
          console.log('添加成功');
          toUrl('/pages/jauntAirLine/passengerInfo/index');
        }
      }
    } finally {
      setLoading(false);
    }
  };

  // 证件类型改变时的处理
  const handleCertTypeChange = (value: number) => {
    updateFormData('certType', value);
    // 清空相关字段
    if (value === 1) {
      // 身份证
      updateFormData('firstName', '');
      updateFormData('nextName', '');
      updateFormData('birthday', '');
      updateFormData('certValidity', '');
      updateFormData('nationality', '');
      updateFormData('issuerCountry', '');
    } else {
      // 其他证件
      updateFormData('name', '');
    }
  };

  return (
    <View className='add-passenger-page'>
      <CustomHeader
        showBackButton={true}
        bgColor={'transparent'}
        title={isEdit ? '编辑乘机人' : '新增乘机人'}
      />

      <View className='form-container'>
        {/* 证件类型 */}
        <View className='form-item'>
          <Text className='label'>证件类型</Text>
          <Picker
            options={[certTypeOptions]}
            value={[formData.certType]}
            onConfirm={selectedOptions => {
              const selected = selectedOptions[0];
              if (selected) {
                handleCertTypeChange(selected.value as number);
              }
            }}
          >
            <View className='picker-value'>
              {certTypeOptions.find(item => item.value === formData.certType)?.text || '请选择'}
            </View>
          </Picker>
        </View>

        {/* 姓名 - 身份证时显示 */}
        {formData.certType === 1 && (
          <View className='form-item'>
            <Text className='label'>姓名</Text>
            <Input
              className='input'
              placeholder='姓名'
              value={formData.name}
              onInput={e => updateFormData('name', e.detail.value)}
            />
          </View>
        )}

        {/* 姓和名 - 非身份证时显示 */}
        {formData.certType !== 1 && (
          <>
            <View className='form-item'>
              <Text className='label'>姓</Text>
              <Input
                className='input'
                placeholder='姓'
                value={formData.firstName}
                onInput={e => updateFormData('firstName', e.detail.value)}
              />
            </View>
            <View className='form-item'>
              <Text className='label'>名</Text>
              <Input
                className='input'
                placeholder='名'
                value={formData.nextName}
                onInput={e => updateFormData('nextName', e.detail.value)}
              />
            </View>
          </>
        )}

        {/* 证件号码 */}
        <View className='form-item'>
          <Text className='label'>证件号码</Text>
          <Input
            className='input'
            placeholder='证件号码'
            value={formData.certNo}
            onInput={e => updateFormData('certNo', e.detail.value)}
          />
        </View>

        {/* 非身份证时的额外字段 */}
        {formData.certType !== 1 && (
          <>
            {/* 有效期至 */}
            <View className='form-item'>
              <Text className='label'>有效期至</Text>
              <DatePicker
                modelValue={formData.certValidity ? new Date(formData.certValidity) : new Date()}
                onConfirm={(selectedOptions, selectedValue) => {
                  updateFormData('certValidity', dayjs(selectedValue).format('YYYY-MM-DD'));
                }}
              >
                <View className='picker-value'>
                  {formData.certValidity || '点击选择证件有效期日期'}
                </View>
              </DatePicker>
            </View>

            {/* 出生日期 */}
            <View className='form-item'>
              <Text className='label'>出生日期</Text>
              <DatePicker
                modelValue={formData.birthday ? new Date(formData.birthday) : new Date()}
                onConfirm={(selectedOptions, selectedValue) => {
                  updateFormData('birthday', dayjs(selectedValue).format('YYYY-MM-DD'));
                }}
              >
                <View className='picker-value'>{formData.birthday || '点击选择出生日期'}</View>
              </DatePicker>
            </View>

            {/* 国籍 */}
            <View className='form-item'>
              <Text className='label'>国籍</Text>
              <View className='picker-value'>中国</View>
            </View>

            {/* 证件签发国 */}
            <View className='form-item'>
              <Text className='label'>证件签发国</Text>
              <View className='picker-value'>中国</View>
            </View>

            {/* 性别 */}
            <View className='form-item'>
              <Text className='label'>性别</Text>
              <View className='radio-group'>
                {genderOptions.map(option => (
                  <View key={option.value} className='radio-item'>
                    <Radio
                      checked={formData.sex === option.value}
                      onChange={() => updateFormData('sex', option.value)}
                    />
                    <Text className='radio-label'>{option.label}</Text>
                  </View>
                ))}
              </View>
            </View>
          </>
        )}

        {/* 手机号 */}
        <View className='form-item'>
          <Text className='label'>手机号</Text>
          <Input
            className='input'
            placeholder='手机号'
            type='number'
            value={formData.mobile}
            onInput={e => updateFormData('mobile', e.detail.value)}
          />
        </View>
      </View>

      {/* 底部按钮 */}
      <BottomCtr>
        <Button className='submit-btn' loading={loading} onClick={handleSubmit}>
          完成
        </Button>
      </BottomCtr>
    </View>
  );
};

export default memo(AddPassenger);
