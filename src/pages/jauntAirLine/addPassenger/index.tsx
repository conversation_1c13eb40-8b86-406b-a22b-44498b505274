import { memo, useState, useEffect } from 'react';
import { View, Text, Input } from '@tarojs/components';
import { <PERSON>ton, Picker, Radio, DatePicker } from '@nutui/nutui-react-taro';
import CustomHeader from '@/components/CustomHeader';
import { toUrl, getUrlParam, toast } from '@/utils';
import './index.less';
import { AddCommonPassengerDTO, UpdateCommonPassengerDTO, NationalityVO } from '@/api/servies/dkx';
import api from '@/api';
import { SUCCESS_CODE } from '@/utils/constant';
import dayjs from 'dayjs';
import BottomCtr from '@/components/BottomCtr';

// 证件类型映射
const idTypeMapping = [
  ["身份证", "identity"],
  ["护照", "passport"],
  ["出生证明", "chusheng"],
  ["军官证", "junguan"],
  ["武警警官证", "jingguan"],
  ["武警士兵证", "shibing"],
  ["外国人永久居住证", "wgryongjiujuliu"],
  ["职工证(文职干部证、义务兵证证、文职人员证)", "zhigong"],
  ["海员证", "haiyuan"],
  ["外交部签发的驻华外交人员证", "zhuhuawaijiao"],
  ["户口所在地公安机关出具的身份证明", "shengfenzhenming"],
  ["学生证", "xuesheng"],
  ["士官证", "shiguan"],
  ["户口簿", "hukoubo"],
  ["回乡证", "huixiang"],
  ["台胞证", "taibao"],
  ["外国人出入境证", "entryexitcertificate"],
  ["其他", "other"]
];

const AddPassenger = () => {
  const [isEdit, setIsEdit] = useState(false);
  const [passengerId, setPassengerId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [nationalityList, setNationalityList] = useState<NationalityVO[]>([]);

  // 表单数据
  const [formData, setFormData] = useState<AddCommonPassengerDTO>({
    name: '',
    firstName: '',
    nextName: '',
    mobile: '',
    sex: '1', // 1男 2女
    certType: 'identity', // 默认身份证
    certNo: '',
    certValidity: '',
    issuerCountry: 'CN',
    nationality: 'CN',
    birthday: '',
  });

  // 证件类型选项
  const certTypeOptions = idTypeMapping.map(([text, value]) => ({
    text,
    value,
  }));

  // 性别选项
  const genderOptions = [
    { label: '男', value: '1' },
    { label: '女', value: '2' },
  ];

  // 获取国籍列表
  const fetchNationalityList = async () => {
    try {
      const { code, data } = await api.v10NationalityCreate();
      if (code === SUCCESS_CODE && data) {
        setNationalityList(data);
      }
    } catch (error) {
      console.error('获取国籍列表失败:', error);
    }
  };

  // 数字证件类型转字符串的映射
  const certTypeNumberToString = (certType?: number): string => {
    const mapping: { [key: number]: string } = {
      1: 'identity',
      2: 'passport',
      3: 'hukoubo',
      4: 'chusheng',
      5: 'other',
    };
    return mapping[certType || 1] || 'identity';
  };

  // 获取乘机人详情（编辑模式）
  const fetchPassengerDetail = async (id: string) => {
    try {
      const { code, data } = await api.v10PassengerListCreate();
      if (code === SUCCESS_CODE && data) {
        const passenger = data.find(item => item.id?.toString() === id);
        if (passenger) {
          setFormData({
            name: passenger.name || '',
            firstName: passenger.firstName || '',
            nextName: passenger.nextName || '',
            mobile: passenger.mobile || '',
            sex: passenger.sex || '1',
            certType: certTypeNumberToString(passenger.certType),
            certNo: passenger.certNo || '',
            certValidity: passenger.certValidity || '',
            issuerCountry: passenger.issuerCountry || 'CN',
            nationality: passenger.nationality || 'CN',
            birthday: passenger.birthday || '',
          });
        }
      }
    } catch (error) {
      console.error('获取乘机人详情失败:', error);
    }
  };

  useEffect(() => {
    // 检查是否为编辑模式
    const editMode = getUrlParam('isEdit');
    const id = getUrlParam('passengerId');

    if (editMode === 'true' && id) {
      setIsEdit(true);
      setPassengerId(id as string);
      // 获取乘机人详情
      fetchPassengerDetail(id as string);
    }

    // 获取国籍列表
    fetchNationalityList();
  }, []);

  // 更新表单数据
  const updateFormData = (key: keyof AddCommonPassengerDTO, value: any) => {
    setFormData(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // 表单验证
  const validateForm = () => {
    if (formData.certType === 'identity') {
      // 身份证
      if (!formData.name) {
        toast.info('请输入姓名');
        return false;
      }
    } else if (formData.certType === 'chusheng') {
      // 出生证明
      if (!formData.name) {
        toast.info('请输入姓名');
        return false;
      }
      if (!formData.birthday) {
        toast.info('请选择出生日期');
        return false;
      }
      if (!formData.certValidity) {
        toast.info('请选择证件有效期');
        return false;
      }
      if (!formData.nationality) {
        toast.info('请选择国籍');
        return false;
      }
      if (!formData.issuerCountry) {
        toast.info('请选择证件签发国');
        return false;
      }
      if (!formData.sex) {
        toast.info('请选择性别');
        return false;
      }
    } else {
      // 其他证件类型
      if (!formData.firstName || !formData.nextName) {
        toast.info('请输入姓和名');
        return false;
      }
      if (!formData.birthday) {
        toast.info('请选择出生日期');
        return false;
      }
      if (!formData.certValidity) {
        toast.info('请选择证件有效期');
        return false;
      }
      if (!formData.nationality) {
        toast.info('请选择国籍');
        return false;
      }
      if (!formData.issuerCountry) {
        toast.info('请选择证件签发国');
        return false;
      }
      if (!formData.sex) {
        toast.info('请选择性别');
        return false;
      }
    }

    if (!formData.certNo) {
      toast.info('请输入证件号码');
      return false;
    }

    if (!formData.mobile) {
      toast.info('请输入手机号');
      return false;
    }

    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.mobile)) {
      toast.info('请输入正确的手机号');
      return false;
    }

    return true;
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      if (isEdit) {
        // 编辑模式
        const updateData: UpdateCommonPassengerDTO = {
          id: Number(passengerId),
          ...formData,
        };
        const { code } = await api.v10PassengerUpdateCreate(updateData);
        if (code === SUCCESS_CODE) {
          toast.info('更新成功');
          toUrl('/pages/jauntAirLine/passengerInfo/index');
        }
      } else {
        // 新增模式
        const { code } = await api.v10PassengerAddCreate(formData);
        if (code === SUCCESS_CODE) {
          toast.info('添加成功');
          toUrl('/pages/jauntAirLine/passengerInfo/index');
        }
      }
    } finally {
      setLoading(false);
    }
  };

  // 证件类型改变时的处理
  const handleCertTypeChange = (value: string) => {
    updateFormData('certType', value);
    // 清空相关字段
    if (value === 'identity') {
      // 身份证
      updateFormData('firstName', '');
      updateFormData('nextName', '');
      updateFormData('birthday', '');
      updateFormData('certValidity', '');
      updateFormData('nationality', 'CN');
      updateFormData('issuerCountry', 'CN');
      updateFormData('sex', '1');
    } else {
      // 其他证件
      updateFormData('name', '');
      updateFormData('nationality', 'CN');
      updateFormData('issuerCountry', 'CN');
    }
  };

  return (
    <View className='add-passenger-page'>
      <CustomHeader
        showBackButton={true}
        bgColor={'transparent'}
        title={isEdit ? '编辑乘机人' : '新增乘机人'}
      />

      <View className='form-container'>
        {/* 证件类型 */}
        <View className='form-item'>
          <Text className='label'>证件类型</Text>
          <Picker
            options={[certTypeOptions]}
            value={[formData.certType]}
            onConfirm={selectedOptions => {
              const selected = selectedOptions[0];
              if (selected) {
                handleCertTypeChange(selected.value as string);
              }
            }}
          >
            <View className='picker-value'>
              {certTypeOptions.find(item => item.value === formData.certType)?.text || '请选择'}
            </View>
          </Picker>
        </View>

        {/* 姓名 - 身份证和出生证明时显示 */}
        {(formData.certType === 'identity' || formData.certType === 'chusheng') && (
          <View className='form-item'>
            <Text className='label'>姓名</Text>
            <Input
              className='input'
              placeholder='请输入姓名'
              value={formData.name}
              onInput={e => updateFormData('name', e.detail.value)}
            />
          </View>
        )}

        {/* 姓和名 - 除身份证和出生证明外的其他证件类型显示 */}
        {formData.certType !== 'identity' && formData.certType !== 'chusheng' && (
          <>
            <View className='form-item'>
              <Text className='label'>姓</Text>
              <Input
                className='input'
                placeholder='请输入姓'
                value={formData.firstName}
                onInput={e => updateFormData('firstName', e.detail.value)}
              />
            </View>
            <View className='form-item'>
              <Text className='label'>名</Text>
              <Input
                className='input'
                placeholder='请输入名'
                value={formData.nextName}
                onInput={e => updateFormData('nextName', e.detail.value)}
              />
            </View>
          </>
        )}

        {/* 证件号码 */}
        <View className='form-item'>
          <Text className='label'>证件号码</Text>
          <Input
            className='input'
            placeholder='请输入证件号码'
            value={formData.certNo}
            onInput={e => updateFormData('certNo', e.detail.value)}
          />
        </View>

        {/* 非身份证时的额外字段 */}
        {formData.certType !== 'identity' && (
          <>
            {/* 有效期至 */}
            <View className='form-item'>
              <Text className='label'>有效期截至</Text>
              <DatePicker
                modelValue={formData.certValidity ? new Date(formData.certValidity) : new Date()}
                onConfirm={(selectedOptions, selectedValue) => {
                  updateFormData('certValidity', dayjs(selectedValue).format('YYYY-MM-DD'));
                }}
              >
                <View className='picker-value'>
                  {formData.certValidity || '请选择证件有效期日期'}
                </View>
              </DatePicker>
            </View>

            {/* 出生日期 */}
            <View className='form-item'>
              <Text className='label'>出生日期</Text>
              <DatePicker
                modelValue={formData.birthday ? new Date(formData.birthday) : new Date()}
                onConfirm={(selectedOptions, selectedValue) => {
                  updateFormData('birthday', dayjs(selectedValue).format('YYYY-MM-DD'));
                }}
              >
                <View className='picker-value'>{formData.birthday || '请选择出生日期'}</View>
              </DatePicker>
            </View>

            {/* 国籍 */}
            <View className='form-item'>
              <Text className='label'>国籍</Text>
              <Picker
                options={[nationalityList.map(item => ({ text: item.name || '', value: item.code || '' }))]}
                value={[formData.nationality]}
                onConfirm={selectedOptions => {
                  const selected = selectedOptions[0];
                  if (selected) {
                    updateFormData('nationality', selected.value);
                  }
                }}
              >
                <View className='picker-value'>
                  {nationalityList.find(item => item.code === formData.nationality)?.name || '请选择国籍'}
                </View>
              </Picker>
            </View>

            {/* 证件签发国 */}
            <View className='form-item'>
              <Text className='label'>证件签发国</Text>
              <Picker
                options={[nationalityList.map(item => ({ text: item.name || '', value: item.code || '' }))]}
                value={[formData.issuerCountry]}
                onConfirm={selectedOptions => {
                  const selected = selectedOptions[0];
                  if (selected) {
                    updateFormData('issuerCountry', selected.value);
                  }
                }}
              >
                <View className='picker-value'>
                  {nationalityList.find(item => item.code === formData.issuerCountry)?.name || '请选择证件签发国'}
                </View>
              </Picker>
            </View>

            {/* 性别 */}
            <View className='form-item'>
              <Text className='label'>性别</Text>
              <View className='radio-group'>
                {genderOptions.map(option => (
                  <View key={option.value} className='radio-item'>
                    <Radio
                      checked={formData.sex === option.value}
                      onChange={() => updateFormData('sex', option.value)}
                    />
                    <Text className='radio-label'>{option.label}</Text>
                  </View>
                ))}
              </View>
            </View>
          </>
        )}

        {/* 手机号 */}
        <View className='form-item'>
          <Text className='label'>手机号</Text>
          <Input
            className='input'
            placeholder='请输入手机号'
            type='number'
            value={formData.mobile}
            onInput={e => updateFormData('mobile', e.detail.value)}
          />
        </View>
      </View>

      {/* 底部按钮 */}
      <BottomCtr>
        <Button className='submit-btn' loading={loading} onClick={handleSubmit}>
          完成
        </Button>
      </BottomCtr>
    </View>
  );
};

export default memo(AddPassenger);
