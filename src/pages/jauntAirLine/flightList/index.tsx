import { memo, useEffect, useState } from 'react';
import { View, Text, ScrollView, Image, Button } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import { flightIcon } from '@/utils/img';
import { _localStorage, toUrl } from '@/utils';
import './index.less';
import { ArrowDown } from '@nutui/icons-react-taro';
import PriceCalendar from '../components/PriceCalendar';
import FlightItem from '../components/FlightItem';
import api from '@/api';
import { SUCCESS_CODE } from '@/utils/constant';
import CustomEmpty from '@/components/CustomEmpty';
import { FlightPlanBO } from '@/api/servies/dkx';

const FlightList = () => {
  const [selectedDate, setSelectedDate] = useState('');
  const [sortType, setSortType] = useState<'time' | 'price'>('time');
  const [flightList, setFlightList] = useState<FlightPlanBO[]>([]);
  const [loading, setLoading] = useState(false);
  //todo 临时注释
  const urlParams = {
    arrName: '满洲里',
    arrCode: 'KHG',
    depName: '海拉尔',
    depCode: 'YC7',
    flightDate: '2025-07-01',
  };
  // const urlParams = JSON.parse(getUrlParam('data') as string);
  console.log(urlParams);

  // 处理日期选择
  const handleDateSelect = (date: string, _day: string) => {
    setSelectedDate(date);
    fetchFlightList({ flightDate: date });
  };

  // 获取航班列表数据
  const fetchFlightList = async (searchData?: any) => {
    setLoading(true);

    // 准备请求参数
    const params = {
      depCode: urlParams?.depCode,
      arrCode: urlParams?.arrCode,
      flightDate: urlParams?.flightDate,
      ...searchData,
    };
    const { code, data } = await api.shortRouteV10FlightCreate(params);
    if (code === SUCCESS_CODE) {
      setFlightList(data || []);
    }
    setLoading(false);
  };

  // 根据排序类型对航班进行排序
  const sortedFlights = [...flightList].sort((a, b) => {
    if (sortType === 'time') {
      return (a.planDepartTime || '').localeCompare(b.planDepartTime || '');
    } else {
      // 使用最低价格进行排序
      const aPrice = a.flightPlanCabins?.[0]?.price || 0;
      const bPrice = b.flightPlanCabins?.[0]?.price || 0;
      return aPrice - bPrice;
    }
  });

  const handleSelectFlight = (flight: FlightPlanBO) => {
    // 跳转到选择舱位页面，传递航班数据
    _localStorage.setItem('flightData', JSON.stringify(flight));
    toUrl(`/pages/jauntAirLine/selectCabin/index`);
  };

  useEffect(() => {
    // 加载航班数据
    fetchFlightList();
  }, []);

  return (
    <View className='flight-list-page'>
      {/* 1. 顶部导航栏 */}
      <CustomHeader showBackButton={true} bgColor={'#3175f9'} title={'航班列表'} />

      {/* 2. 价格日历 */}
      <PriceCalendar
        onDateSelect={handleDateSelect}
        selectedDate={selectedDate}
        depCode={urlParams.depCode}
        arrCode={urlParams.arrCode}
        flightDate={urlParams.flightDate}
      />

      {/* 3. 城市信息和搜索结果 */}
      <View className='city-info'>
        <View className='cities'>
          <Image src={flightIcon} className={'flight-icon'} />
          {urlParams.depName || '出发地'}
          <Text className={'split-text'}>到</Text>
          {urlParams.arrName || '目的地'}
        </View>
        <Text className='result-count'>
          (共<Text>{flightList.length}</Text>个航班)
          {loading && <Text className='loading-text'> 加载中...</Text>}
        </Text>
      </View>

      {/* 4. 航班列表 */}
      <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
        <View className='flight-list-container'>
          {loading ? (
            // 加载状态
            <View className='loading-container'>
              <Text>正在搜索航班...</Text>
            </View>
          ) : sortedFlights.length > 0 ? (
            // 有数据时显示航班列表
            sortedFlights.map((flight, index) => {
              // 获取最低价格
              const minPrice =
                flight.flightPlanCabins?.reduce((min, cabin) => {
                  return cabin.price && cabin.price < min ? cabin.price : min;
                }, flight.flightPlanCabins?.[0]?.price || 0) || 0;

              return (
                <View key={flight.flightNo || index} className='flight-card'>
                  <FlightItem flight={flight} />
                  <View
                    className='card-box price-section'
                    onClick={() => handleSelectFlight(flight)}
                  >
                    <Text className='price'>
                      ¥<Text>{minPrice}</Text>起
                    </Text>
                    <Button className='select-btn'>选择</Button>
                  </View>
                </View>
              );
            })
          ) : (
            <CustomEmpty />
          )}
        </View>
      </ScrollView>

      {/* 5. 底部排序功能 */}
      <View className='sort-bar'>
        <View
          className={`sort-item ${sortType === 'time' ? 'active' : ''}`}
          onClick={() => setSortType('time')}
        >
          <Text className='sort-text'>时间</Text>
          <Text className='sort-subtext'>由早至晚</Text>
        </View>
        <View className='divider'></View>
        <View
          className={`sort-item ${sortType === 'price' ? 'active' : ''}`}
          onClick={() => setSortType('price')}
        >
          <Text className='sort-text'>价格</Text>
          <ArrowDown width={12} height={12} color={sortType === 'price' ? '#3175f9' : '#666'} />
        </View>
        <View className='divider'></View>
        <View className='sort-item' onClick={() => fetchFlightList()}>
          <Text className='sort-text'>刷新</Text>
        </View>
      </View>
    </View>
  );
};

export default memo(FlightList);
