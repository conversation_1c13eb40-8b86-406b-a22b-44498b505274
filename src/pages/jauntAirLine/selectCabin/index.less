@color-blue: #3175f9;

.select-cabin-page {
  min-height: 100vh;
  width: 100%;
  //background-color: #f0f4fa;
  background: linear-gradient(180deg, #1663f8 0%, rgba(22, 99, 248, 0) 30%, #f0f4fa 100%);

  .scrollview {
    height: calc(100vh - 140px);
    padding: 16px;
    box-sizing: border-box;
    background: #fafafa;
    border-radius: 24px 24px 0 0;
  }

  .flight-info-card {
    //margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin: 16px;
    .flight-info-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: rgba(28, 28, 28, 0.8);
      font-size: 12px;
    }

    .nut-collapse-item-header:after {
      border-bottom: none;
    }
    .nut-collapse-item-header,
    .nut-collapse-item-content-text {
      padding: 0;
    }
    .flight-info-section {
      margin-top: 16px;
      background: rgba(28, 28, 28, 0.05);
    }
  }

  .cabin-selection-container {
    .cabin-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      border: 1px solid;
      border-image-source: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
      box-shadow: 0 0 12px rgba(39, 17, 17, 0.08);

      image {
        width: 12px;
        height: 14px;
      }
      .cabin-header {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 4px;
        color: #1d1f20;
        .cabin-name {
          font-size: 16px;
          font-weight: bold;
        }
        .cabin-short-name {
          font-size: 12px;
        }
      }
      .cabin-features {
        font-size: 12px;
        color: rgba(28, 28, 28, 0.8);
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 8px;
      }
      .price-box {
        font-size: 12px;
        color: #f84f2a;
        margin-bottom: 8px;
        text {
          font-size: 20px;
          font-weight: bold;
          margin: 0 2px;
        }
      }
      .select-btn {
        width: 62px;
        height: 32px;
        background: #1663f8;
        color: #fff;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        border-radius: 99px;
      }
    }
  }
}
