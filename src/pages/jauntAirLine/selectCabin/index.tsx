import { memo, useEffect, useState } from 'react';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import { Collapse } from '@nutui/nutui-react-taro';
import CustomHeader from '@/components/CustomHeader';
import { _localStorage, formatDate, toUrl } from '@/utils';
import './index.less';
import { ArrowDown, ArrowRight } from '@nutui/icons-react-taro';
import FlightItem from '@/pages/jauntAirLine/components/FlightItem';
import CustomTag from '@/components/CustomTag';
import { packetIcon, ticketIcon } from '@/utils/img';
import { FlightCabinBO, FlightPlanBO } from '@/api/servies/dkx';

const SelectCabin = () => {
  const [selectedCabin, setSelectedCabin] = useState<string | null>(null);
  const [flightInfo, setFlightInfo] = useState<FlightPlanBO | null>(null);
  const [cabins, setCabins] = useState<FlightCabinBO[]>([]);

  useEffect(() => {
    // 从 URL 参数获取航班数据
    const flightDataStr = _localStorage.getItem('flightData');
    if (flightDataStr) {
      const flightData: FlightPlanBO = JSON.parse(flightDataStr as string);
      setFlightInfo(flightData);
      // 设置舱位信息
      if (flightData.flightPlanCabins) {
        setCabins(flightData.flightPlanCabins);
        console.log(flightData.flightPlanCabins);
      }
    }
  }, []);

  // 计算折扣
  const calculateDiscount = (price?: number, originalPrice?: number) => {
    if (!price || !originalPrice || originalPrice === 0) return null;
    return ((price / originalPrice) * 10).toFixed(1);
  };

  const handleSelectCabin = (cabinCode: string) => {
    setSelectedCabin(cabinCode);
  };

  const handleContinue = (cabin: FlightCabinBO) => {
    toUrl(`/pages/jauntAirLine/passengerInfo/index?cabinData=${JSON.stringify(cabin)}`);
  };

  if (!flightInfo) {
    return (
      <View className='select-cabin-page'>
        <CustomHeader showBackButton={true} bgColor={'transparent'} title={'选择舱位'} />
        <View className='loading-container'>
          <Text>加载中...</Text>
        </View>
      </View>
    );
  }

  return (
    <View className='select-cabin-page'>
      <CustomHeader showBackButton={true} bgColor={'transparent'} title={'选择舱位'} />
      <View className='card-box flight-info-card'>
        <Collapse expandIcon={<ArrowDown />}>
          <Collapse.Item
            title={
              <View className={'flight-info-title'}>
                <CustomTag type={'gray'}>出发</CustomTag>
                <Text>{formatDate(flightInfo.flightDate)}</Text>
                <View className={'split-line'} />
                <View>
                  {flightInfo.departCity} - {flightInfo.arriveCity}
                </View>
              </View>
            }
          >
            <FlightItem flight={flightInfo} />
          </Collapse.Item>
        </Collapse>
      </View>
      <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
        <View className='cabin-selection-container'>
          {cabins.map((cabin, index) => {
            const discount = calculateDiscount(cabin.price, cabin.originalPrice);
            return (
              <View
                className={`card-box cabin-item ${selectedCabin === cabin.code ? 'selected' : ''}`}
                key={cabin.code || index}
                onClick={() => handleSelectCabin(cabin.code || '')}
              >
                <View className={'cabin-left'}>
                  <View className={'cabin-header'}>
                    <Text className={'cabin-name'}>{cabin.cabinType}</Text>
                    <Text className={'cabin-short-name'}>{cabin.code}舱</Text>
                    {discount && Number(discount) < 10 && (
                      <CustomTag bgColor={'rgba(248, 190, 42, 0.2)'} color={'#1D1F20'}>
                        {discount}折
                      </CustomTag>
                    )}
                  </View>
                  <View className={'cabin-features'}>
                    <Image src={packetIcon} />
                    <Text>行李：</Text>
                    {cabin?.baggage?.freeHandBaggage && (
                      <Text>手提{cabin?.baggage?.handBaggage?.limitWeight}kg</Text>
                    )}
                    {cabin?.baggage?.freeHandBaggage && cabin?.baggage?.freeRegBaggage && (
                      <View className={'split-line'} />
                    )}
                    {cabin?.baggage?.freeRegBaggage && (
                      <Text>托运{cabin?.baggage?.regBaggage?.limitWeight}kg</Text>
                    )}
                  </View>
                  <View className={'cabin-features'}>
                    <Image src={ticketIcon} />
                    <Text>退改：</Text>
                    <Text>{cabin.refundRule || '0元起'}</Text>
                    <ArrowRight size={12} />
                  </View>
                  <View className={'cabin-features'}>
                    <Text>剩余座位：{cabin.seat || 0}个</Text>
                  </View>
                </View>
                <View className={'cabin-right'} onClick={() => handleContinue(cabin)}>
                  <View className={'price-box'}>
                    ¥<Text>{cabin.price || 0}</Text>起
                  </View>
                  <View className={'select-btn'}>订</View>
                </View>
              </View>
            );
          })}
        </View>
      </ScrollView>
    </View>
  );
};

export default memo(SelectCabin);
