import cx from 'classnames';

import BottomCtr from '@/components/BottomCtr';
import CustomHeader from '@/components/CustomHeader';
import { Button, View, Text, Image } from '@tarojs/components';
import { arrowRightSLine } from '@/utils/img';
import { toUrl } from '@/utils';

const AirTourOrderDetail = () => {
  return (
    <View className='min-h-screen  bg-primary-linear'>
      <CustomHeader showBackButton title='订单详情' bgColor='transparent' />

      <View className='px-16 py-12'>
        {/* 订单详情卡片 */}
        <View className='bg-white rounded-12 px-16 py-8 mb-12 shadow-sm'>
          <Text className='text-20 font-semibold text-1D1F20 mb-12 block'>
            南头直升机场空中游览
          </Text>

          <View className='mt-12'>
            {[
              { label: '套餐类型', value: '千岛湖3300米跳伞+教练含跳伞装备' },
              { label: '出游人群', value: '成人' },
              { label: '出行日期', value: '2024-04-20+教练含跳伞装备' },
            ].map((item, index) => {
              return (
                <View
                  className={cx('flex', {
                    'mt-12': index !== 0,
                  })}
                  key={index}
                >
                  <View className='text-12 w-54'>{item.label}</View>
                  <Text className='text-12 font-medium'>{item.value}</Text>
                </View>
              );
            })}
          </View>
        </View>

        {/* 使用有效期卡片 */}
        <View className='bg-white rounded-12 px-16 py-8 mb-16 shadow-sm'>
          <View className='text-14 font-semibold text-1D1F20 mb-4'>使用有效期</View>

          <View className='text-12 text-737578 mb-12'>2023.12.27 - 2024.06.27有效</View>

          <View className='bg-F2F6FC rounded-8 px-8 py-6 flex items-center'>
            <Text className='text-12 text-4F5170 flex-1'>购买须知</Text>
            <Image src={arrowRightSLine} className='w-14 h-14' />
          </View>
        </View>

        {/* 飞行架次 */}
        <View className='bg-white rounded-12 px-16 py-8 mb-16 shadow-sm'>
          <View className='text-14 font-semibold text-1D1F20 mb-12'>飞行架次</View>

          {/* 飞行架次列表 */}
          <View>
            {[
              {
                id: 1,
                name: '172松涛天湖飞行大约40分钟',
                time: '08:00-18:30',
                takeoff: '待定',
                status: '剩余5',
                statusColor: 'text-FF7346',
                selected: false,
              },
              {
                id: 2,
                name: '172松涛天湖飞行大约40分钟',
                time: '08:00-18:30',
                takeoff: '待定',
                status: '剩余5',
                statusColor: 'text-white',
                selected: true,
              },
              {
                id: 3,
                name: '172松涛天湖飞行大约40分钟',
                time: '08:00-18:30',
                takeoff: '待定',
                status: '成团还需5人',
                statusColor: 'text-FF7346',
                selected: false,
              },
              {
                id: 4,
                name: '172松涛天湖飞行大约40分钟',
                time: '08:00-18:30',
                takeoff: '待定',
                status: '剩余20',
                statusColor: 'text-737578',
                selected: false,
              },
            ].map((item, index) => (
              <View
                key={item.id}
                className={cx('rounded-8 px-8 py-6 flex flex-col', {
                  'bg-F2F6FC': !item.selected,
                  'bg-primary': item.selected,
                  'mt-8': index !== 0,
                })}
              >
                <View className='flex justify-between items-center mb-4'>
                  <Text
                    className={cx('text-12 font-medium', {
                      'text-1D1F20': !item.selected,
                      'text-white': item.selected,
                    })}
                  >
                    {item.name}
                  </Text>
                  <Text className={cx('text-10', item.statusColor)}>{item.status}</Text>
                </View>
                <View className='flex'>
                  <Text
                    className={cx('text-12 mr-12', {
                      'text-737578': !item.selected,
                      'text-white opacity-75': item.selected,
                    })}
                  >
                    飞行时间:{item.time}
                  </Text>
                  <Text
                    className={cx('text-12', {
                      'text-737578': !item.selected,
                      'text-white opacity-75': item.selected,
                    })}
                  >
                    起降时间:{item.takeoff}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* 选择乘客 */}
        <View className='bg-white rounded-12 px-16 py-12 mb-16 shadow-sm'>
          <View className='flex items-center mb-12'>
            <Text className='text-FF7346 mr-2'>*</Text>
            <Text className='text-14 font-semibold text-1D1F20'>选择乘客</Text>
          </View>

          {/* 乘客标签 */}
          <View className='flex items-center mb-16 overflow-x-auto'>
            {/* 实际应用中，这里可能需要用 ScrollView 实现更好的滚动效果 */}
            {[
              { id: 'A', name: '乘客A', selected: true },
              { id: 'B', name: '乘客B', selected: false },
              { id: 'C', name: '乘客C', selected: false },
              { id: 'D', name: '乘客D', selected: false },
              { id: 'E', name: '乘客E', selected: false },
              {
                id: 'ADD',
                name: (
                  <View className='flex items-center'>
                    <View>新增</View>
                    {/* 这里可以放一个加号图标 */}
                    <View className='ml-4'>+</View>
                  </View>
                ),
                selected: false,
              },
            ].map(tag => (
              <View
                key={tag.id}
                className={cx('flex-shrink-0 rounded-8 px-10 py-6 mr-12 text-12', {
                  'bg-primary text-white': tag.selected,
                  'bg-F2F6FC text-1D1F20': !tag.selected,
                })}
              >
                {tag.name}
              </View>
            ))}
          </View>

          {/* 乘客信息 */}
          {[
            {
              name: '安妮',
              type: '成人',
              idCard: '5139**********048',
              isSelf: true,
            },
            {
              name: '安小妮',
              type: '儿童',
              idCard: '5139**********048',
              isSelf: false,
            },
          ].map((passenger, index) => (
            <View key={index} className='mb-8 border-b-1 border-E6EAF0 pb-8'>
              <View className='flex items-center justify-between'>
                <View>
                  <View className='flex items-center'>
                    <Text className='text-14 font-medium text-1D1F20 mr-4'>{passenger.name}</Text>
                    {passenger.isSelf && (
                      <View className='flex items-center border-1 border-primary rounded-4 px-4 py-2 mr-4'>
                        <Text className='text-10 text-primary'>本人</Text>
                      </View>
                    )}
                    <Text className='text-12 text-1D1F20'>{passenger.type}</Text>
                  </View>
                  <Text className='text-12 text-737578'>身份证：{passenger.idCard}</Text>
                </View>
                <Image src={arrowRightSLine} className='w-14 h-14' />
              </View>
            </View>
          ))}

          {/* 联系电话 */}
          <View className='flex items-center'>
            <View className='text-14 text-737578 mr-8'>联系电话</View>

            <View className='flex items-center'>
              <Text className='text-14 text-1D1F20 mr-4'>+86</Text>
              <Image src={arrowRightSLine} className='w-14 h-14 mr-4' />
              <Text className='text-14 text-737578'>173****3102</Text>
            </View>
          </View>
        </View>

        {/* 保险 */}
        <View className='bg-white rounded-12 px-16 py-12 mb-16 shadow-sm'>
          <View className='flex items-center mb-12'>
            <Text className='text-FF7346 mr-2'>*</Text>
            <Text className='text-14 font-semibold text-1D1F20'>保险</Text>
          </View>

          {/* 保险选项 */}
          <View className='bg-F2F6FC rounded-8 px-8 py-6 flex items-center justify-between'>
            <View>
              <View className='flex items-center'>
                <Text className='text-12 text-1D1F20'>门票意外险</Text>
                <Text className='text-10 text-737578 ml-4'>3元/份起步</Text>
              </View>
              <Text className='text-10 text-737578 mt-4'>(意外事故10万,社保内医疗1万)</Text>
            </View>

            {/* 圆形选择按钮 */}
            <View className='w-14 h-14 rounded-full border-1 border-ACADFF flex items-center justify-center'>
              {/* 如果选中，可以添加一个内圆 */}
              {/* <View className="w-12 h-12 rounded-full bg-primary"></View> */}
            </View>
          </View>
        </View>
      </View>

      <BottomCtr className='flex justify-between items-center'>
        <View className='flex'>
          <Text>实付</Text>
          <Text>¥798.00</Text>
        </View>
        <View>
          <Button
            className='h-48 rounded-full bg-primary px-32 text-white flex items-center text-16 font-semibold'
            onClick={() => {
              toUrl('/pages/orders/travelOrders/orderDetails/index');
            }}
          >
            提交订单
          </Button>
        </View>
      </BottomCtr>
    </View>
  );
};

export default AirTourOrderDetail;
