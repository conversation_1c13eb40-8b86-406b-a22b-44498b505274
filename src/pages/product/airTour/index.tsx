/*
 * @Author: lengkj <EMAIL>
 * @Date: 2025-04-15 11:42:31
 * @LastEditors: lengkj <EMAIL>
 * @LastEditTime: 2025-04-16 19:39:36
 * @Description: 空中游览
 */
import { useEffect, useState } from "react";
import cx from "classnames";
import { navigateTo } from "@tarojs/taro";

import { Button, View, Text, Image } from "@tarojs/components";
import { Calendar } from "@nutui/nutui-react-taro";
import CustomActionSheet from "@/components/CustomActionSheet";
import AirTourCard from "./AirTourCard";

import { arrowDownSFill, arrowDownSFillSelect } from "@/utils/img";

interface IAirTourProps {
  className?: string;
}

const AirTour = ({ className }: IAirTourProps) => {
  const nav = [
    { label: "热度从高到低", value: "1" },
    { label: "价格从高到低", value: "2" },
    { label: "销量", value: "3" },
    { label: "飞行基地", value: "4" },
  ];

  const [current, setCurrent] = useState(nav[0]);
  const [visibleInfo, setVisibleInfo] = useState({ visible: false });
  const [flightBase, setFlightBase] = useState({ area: "1", base: "0" });

  useEffect(() => {
    if (current.value === "4") {
      setVisibleInfo({ visible: true });
    }
  }, [current]);

  return (
    <View className={cx(className)}>
      {/* header */}
      <View className="flex justify-start overflow-y-auto">
        {nav.map((item, index) => (
          <View key={item.value} className="flex-shrink-0">
            <Button
              className={cx(
                "h-28 leading-28 text-12 px-12 rounded-8 flex items-center",
                {
                  "text-primary": current.value === item.value,
                  "mr-4": index !== nav.length - 1,
                }
              )}
              onClick={() => setCurrent(item)}
            >
              <View className="mr-2">{item.label}</View>
              <Image
                src={
                  current.value === item.value
                    ? arrowDownSFillSelect
                    : arrowDownSFill
                }
                className="w-14 h-14"
                mode="aspectFit"
              />
            </Button>
          </View>
        ))}
      </View>

      {[{}, {}, {}, {}].map((item, index) => {
        const { id, ...rest } = item;

        return (
          <AirTourCard
            key={index}
            className={cx("mt-12", {})}
            top={index < 3 ? index + 1 : undefined} // 测试用
            onClick={() => {
              navigateTo({
                url: `/pages/product/airTourDetail/index?id=${id}`,
              });
            }}
            {...rest}
          />
        );
      })}

      <Calendar visible={false} />

      <CustomActionSheet
        visible={visibleInfo.visible}
        title="飞行基地"
        onConfirm={() => {
          setVisibleInfo({ visible: false });
        }}
        onCancel={() => {
          setVisibleInfo({ visible: false });
        }}
      >
        <View className="h-360 flex p-12">
          {/* left */}
          <View className="bg-F2F6FC flex-shrink-0 p-8 rounded-4 overflow-y-auto">
            {[
              { label: "成都", value: "1" },
              { label: "绵阳", value: "2" },
              { label: "内江", value: "3" },
              { label: "xxx", value: "4" },
              { label: "xxx", value: "5" },
              { label: "xxx", value: "6" },
              { label: "xxx", value: "7" },
              { label: "xxx", value: "8" },
              { label: "xxx", value: "9" },
              { label: "xxx", value: "10" },
              { label: "x2x", value: "11" },
            ].map((item, index) => {
              return (
                <View key={index}>
                  <Button
                    className={cx(
                      "min-w-90 text-12 rounded-8 h-34 flex justify-center items-center",
                      {
                        "mt-8": index !== 0,
                        "bg-primary text-white": flightBase.area === item.value,
                      }
                    )}
                    onClick={() => {
                      setFlightBase((old) => ({
                        ...old,
                        area: item.value,
                        base: "",
                      }));
                    }}
                  >
                    {item.label}
                  </Button>
                </View>
              );
            })}
          </View>

          {/* right */}
          <View className="ml-16 flex flex-wrap overflow-y-auto">
            {[
              { label: "全部", value: "0" },
              { label: "xxx", value: "2" },
              { label: "xxx", value: "3" },
              { label: "xxx", value: "4" },
              { label: "xxx", value: "5" },
              { label: "xxx", value: "6" },
              { label: "xxx", value: "7" },
              { label: "xxx", value: "8" },
              { label: "xxx", value: "9" },
              { label: "xxx", value: "10" },
              { label: "x2x", value: "11" },
            ].map((item, index) => {
              return (
                <View key={index}>
                  <Button
                    className={cx(
                      "text-12 rounded-8 h-34 flex justify-center items-center",
                      {
                        "ml-8": index !== 0,
                        "text-primary": flightBase.base === item.value,
                      }
                    )}
                    onClick={() => {
                      setFlightBase((old) => ({
                        ...old,
                        base: item.value,
                      }));
                    }}
                  >
                    {item.label}
                  </Button>
                </View>
              );
            })}
          </View>
        </View>
      </CustomActionSheet>
    </View>
  );
};

export default AirTour;
