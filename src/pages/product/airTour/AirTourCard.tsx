import cx from "classnames";

import { View, Text, Image } from "@tarojs/components";
import { wxkn, top1, top2, top3, play } from "@/utils/img";

interface IAirTourCardProps {
  className?: string;
  top?: number;
  onClick?: () => void;
}

const AirTourCard = ({ className = "", top, onClick }: IAirTourCardProps) => {
  const renderTop = () => {
    if (top === 1) {
      return top1;
    }
    if (top === 2) {
      return top2;
    }
    if (top === 3) {
      return top3;
    }
  };

  return (
    <View
      className={cx("flex rounded-8 bg-white p-10 shadow-sm", className)}
      onClick={onClick}
    >
      <View className="relative">
        <View className="w-100 h-100 mr-10 rounded-8 overflow-hidden bg-primary">
          <Image src="" className="" mode="aspectFit" />
        </View>

        {top && (
          <View
            className="w-40 h-54 absolute z-10"
            style={{
              top: -16,
              left: -12,
            }}
          >
            <Image src={renderTop()} className="h-full" mode="aspectFit" />
          </View>
        )}

        {true && (
          <View
            className="absolute w-92 z-10 bg-white rounded-4 text-10 flex items-center justify-center"
            style={{
              bottom: 4,
              left: 4,
            }}
          >
            免费进入场地
          </View>
        )}

        {false && (
          <View
            className="absolute w-18 h-18 z-10"
            style={{
              bottom: 4,
              right: 16,
            }}
          >
            <Image src={play} className="h-full" mode="aspectFit" />
          </View>
        )}
      </View>

      {/* right */}
      <View className="flex flex-col justify-between">
        {/* header */}
        <View>
          <View className="flex justify-start mb-4">
            <View className="w-54 h-20 mr-4">
              <Image src={wxkn} className="h-full" mode="aspectFit" />
            </View>
            <View>
              <Text className="text-14 font-medium">南头直升机场空中游览</Text>
            </View>
          </View>

          {true && (
            <View className="flex mb-4">
              {["飞行 18 公里", "可停车", "可现场预约"].map((item, index) => (
                <View
                  key={index}
                  className={cx("border-1 border-E6EAF0 rounded-4 px-4", {
                    "ml-4": index !== 0,
                  })}
                >
                  <Text className="text-10 text-737578">{item}</Text>
                </View>
              ))}
            </View>
          )}

          <View className="text-10 text-737578">09:00-18:00 17:00停止入场</View>
        </View>

        {/* footer */}
        <View className="flex items-center justify-between">
          <View className="flex items-center">
            <Text className="text-12 font-semibold text-primary mr-4">
              ¥{150}
            </Text>
            {true && (
              <Text className="text-10 text-C4C7CA line-through">¥{1280}</Text>
            )}
          </View>

          <View className="text-10 text-C4C7CA">已售180</View>
        </View>
      </View>
    </View>
  );
};

export default AirTourCard;
