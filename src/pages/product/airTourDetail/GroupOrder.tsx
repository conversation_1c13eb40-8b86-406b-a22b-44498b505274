import { useState } from "react";
import cx from "classnames";

import { Button, Text, Image, View } from "@tarojs/components";
import CustomActionSheet from "@/components/CustomActionSheet/index";

import { arrowRightSLine } from "@/utils/img";

interface IGroupOrderProps {
  className?: string;
}

const GroupOrder = ({ className = "" }: IGroupOrderProps) => {
  const [visible, setVisible] = useState(false);

  const renderInfo = () => {
    return (
      <View className="bg-FFF3EB flex justify-between items-center px-12 mb-12 py-8 rounded-8">
        <View>
          <View className="text-14 font-medium mb-2">2024-04-20 14:20</View>
          <View className="text-12 text-737578">已拼3人,还剩2人可直接成行</View>
        </View>

        <View>
          <View className="text-F3564B text-12 mb-2">15:20:12后结束</View>

          <Button className="bg-F3564B text-white text-12 rounded-4 px-6 py-2 leading-normal">
            直接拼团
          </Button>
        </View>
      </View>
    );
  };

  return (
    <View className={cx("", className)}>
      <View className="px-16 py-8 bg-white rounded-12  shadow-sm">
        <View className="flex justify-between items-center mb-12">
          <View className="text-14 font-medium">拼单列表</View>

          <View>
            <Button
              className="flex items-center leading-normal px-0 bg-transparent"
              onClick={() => {
                setVisible(true);
              }}
            >
              <Text className="text-12 text-737578 mr-2">全部</Text>
              <Image src={arrowRightSLine} className="w-14 h-14" />
            </Button>
          </View>
        </View>

        {renderInfo()}
      </View>

      <CustomActionSheet
        visible={visible}
        title="拼单列表"
        onCancel={() => setVisible(false)}
      >
        <View className="p-12 max-h-360 overflow-y-auto">
          {[{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}].map(
            (item, index) => {
              return renderInfo();
            }
          )}
        </View>
      </CustomActionSheet>
    </View>
  );
};

export default GroupOrder;
