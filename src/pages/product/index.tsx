/*
 * @Author: lengkj <EMAIL>
 * @Date: 2025-04-14 16:18:36
 * @LastEditors: lengkj <EMAIL>
 * @LastEditTime: 2025-04-15 13:56:15
 * @Description: 产品页面
 */

import { memo, useState } from "react";
import cx from "classnames";

import { View, Image, Button } from "@tarojs/components";
import CustomHeader from "@/components/CustomHeader";
import AirTour from "./airTour";

import { productBg } from "@/utils/img";

const Product = () => {
  const pageNav = [
    { label: "空中游览", value: "1", com: <AirTour className="mt-12" /> },
    { label: "跳伞", value: "2" },
  ];
  const [current, setCurrent] = useState(pageNav[0]);

  return (
    <View className="">
      <CustomHeader
        showBackButton
        bgColor="transparent"
        title={current.label}
      />

      <Image src={productBg} className="w-full h-200 fixed top-0 z-bottom" />

      <View className="px-16 py-12">
        <View className="flex justify-start">
          {pageNav.map((item, index) => (
            <View key={item.value}>
              <Button
                className={cx("h-28 leading-28 text-12 px-12 rounded-8", {
                  "bg-primary text-white": current.value === item.value,
                  "mr-4": index !== pageNav.length - 1,
                })}
                onClick={() => setCurrent(item)}
              >
                {item.label}
              </Button>
            </View>
          ))}
        </View>

        <View>{current.com}</View>
      </View>
    </View>
  );
};
export default memo(Product);
