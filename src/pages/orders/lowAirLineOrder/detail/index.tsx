import { memo } from 'react';
import { View, Text, Button, Image } from '@tarojs/components';
import './index.less';
import CustomHeader from '@/components/CustomHeader';
import AirLineCard from '@/pages/lowAltitudeAirline/compoents/AirLineCard';
import { lowAirLineOrderDetailBg, yellowFlight } from '@/utils/img';
import OrderSteps from '../components/OrderSteps';

const LowAirLineOrderDetail = () => {
  return (
    <View
      className='low-airline-order-detail'
      style={{ background: `url(${lowAirLineOrderDetailBg}) no-repeat center center/cover` }}
    >
      {/* 头部区域 */}
      <CustomHeader title={'订单详情'} bgColor={'transparent'} showBackButton={true} />
      <View className={'content-container'}>
        {/* 订单状态 */}
        <View className='customizing-status'>
          <Image src={yellowFlight} className='customizing-icon' />

          <Text className='customizing-text'>方案定制中</Text>
        </View>

        {/* 提示语 */}
        <View className='notice'>
          <Text>定制师正在加急为您定制方案，如有任何需求可联系定制师~</Text>
        </View>

        {/* 订单主要内容 */}
        <View className='card-box content '>
          <AirLineCard type={'orderDetail'} />
          <View className={'split-dash-line'} />
          {/* 行程信息 */}
          <View className='trip-info'>
            <View className='title'>行程信息</View>
            <View className={'card-box info'}>
              <View className='info-item'>
                <Text className='label'>预计出行人数</Text>
                <Text className='value'>10人</Text>
              </View>
              <View className='info-item'>
                <Text className='label'>行程类型</Text>
                <Text className='value'>单程</Text>
              </View>
            </View>
          </View>

          {/* 联系人信息 */}
          <View className='contact-info'>
            <View className='title'>联系人信息</View>
            <View className='card-box info'>
              <View className='info-item'>
                <Text className='label'>联系人姓名</Text>
                <Text className='value'>安妮</Text>
              </View>
              <View className='info-item'>
                <Text className='label'>联系方式</Text>
                <Text className='value'>133-1234-1234</Text>
              </View>
            </View>
          </View>
        </View>

        {/* 订单状态步骤 */}
        <View className='card-box'>
          <OrderSteps currentStep={2} />
        </View>

        {/* 底部按钮 */}
        <View className='bottom-actions'>
          <Button className='action-btn cancel'>取消订单</Button>
          <Button className='action-btn change'>改期</Button>
        </View>
      </View>
    </View>
  );
};

export default memo(LowAirLineOrderDetail);
