// 变量参考： node_modules/@nutui/nutui-react-taro/dist/styles/theme-default.scss
@color-primary: var(--color-primary); /*主色*/

@color-text-primary: #2b2b32;

@color-blue-f8: #1663f8;

@color-white: #fff;

@color-black: #000;

@color-gray-99: #999999;
@color-gray-66: #666666;
@color-gray-33: #333333;
@color-gray-cc: #cccccc;
@color-gray-98: #828998;

page {
  --color-primary: #1466e1;

  --nutui-color-primary: var(--color-primary);
  --nutui-color-primary-stop-1: var(--color-primary);
  --nutui-color-primary-stop-2: var(--color-primary);
  //tab
  --nutui-tabs-titles-item-active-color: @color-text-primary;
  --nutui-tabs-line-border-radius: 12px;
  --nutui-tabs-titles-background-color: #e8f0ff;
  --nutui-tabs-titles-item-color: #525b67;
  --nutui-tabs-titles-height: 36px;
  //cell
  --nutui-cell-title-color: @color-text-primary;
  --nutui-cell-group-title-color: @color-text-primary;
  --nutui-cell-group-wrap-margin: 0;
}
