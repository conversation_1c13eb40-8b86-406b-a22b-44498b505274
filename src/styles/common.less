@import './variables.less';
@import './tailwindcss.less';

@safe-bottom-constant: constant(safe-area-inset-bottom);
@safe-bottom-env: env(safe-area-inset-bottom);

// ------------------------------------------------
.card-box {
  background: #fff;
  box-sizing: border-box;
  padding: 20px 16px;
  border-radius: 12px;
  box-shadow: 0 0 8px 0 #ffffffd9 inset;
}
.border-box {
  box-sizing: border-box;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #e9e9f2;
}
.primary-btn {
  width: 100%;
  height: 50px;
  line-height: 50px;
  border-radius: 6px;
  text-align: center;
  font-size: 14px;
  color: #fff;
  background-color: @color-primary!important;
}
.normal-btn {
  width: 100%;
  height: 50px;
  line-height: 50px;
  border-radius: 6px;
  text-align: center;
  font-size: 14px;
  color: #565e66;
  background-color: transparent !important;
  border: 1px solid #d3d8e5;
}

.bg-primary-linear {
  background: linear-gradient(180deg, #1663f8 0%, #f5f3f3 45%);
}

.placeholder {
  color: #9aa2ca;
}
.tag-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  gap: 10px;
}
.tags {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  box-sizing: border-box;
  padding: 6px 8px;
  background: #f5f6fa;
  color: #4f5170;
  font-size: 12px;
  &.active {
    background: #124072;
    color: #fff;
  }
}
.split-dash-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #9aa2ca;
  margin-bottom: 16px;
}
.split-line {
  width: 1px;
  height: 14px;
  background: rgba(28, 28, 28, 0.2);
}
