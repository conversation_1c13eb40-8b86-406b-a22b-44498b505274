import { View, Text, Image } from '@tarojs/components';
import { customerService } from '@/utils/img';

interface IOnlineServiceProps {
  className?: string;
}

const OnlineService = ({ className = '' }: IOnlineServiceProps) => {
  return (
    <View
      className={`bg-white rounded-12 shadow-sm flex items-center justify-center py-12 ${className}`}
      onClick={() => {
        // 处理在线客服点击事件
        console.log('联系在线客服');
      }}
    >
      <Image src={customerService} className='w-20 h-20 mr-4' />
      <Text className='text-14 text-4F5170'>在线客服</Text>
    </View>
  );
};

export default OnlineService;
