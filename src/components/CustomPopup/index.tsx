import cx from 'classnames';

import { Button, View, Image } from '@tarojs/components';
import { Popup, BasePopup, TaroPopupProps } from '@nutui/nutui-react-taro';
import { closeCircleFill } from '@/utils/img';

interface ICustomPopupProps extends Partial<TaroPopupProps> {}

const CustomPopup = ({ title, className = '', onClose, children, ...rest }: ICustomPopupProps) => {
  return (
    <Popup
      className={cx('w-full max-w-90vw rounded-12 bg-white', className)}
      onClose={onClose}
      {...rest}
    >
      {/* Title */}
      <View className='flex justify-between items-center px-16 pt-16 pb-12'>
        <View className='text-20 font-semibold'>{title}</View>

        <View>
          <Button className='p-0 bg-transparent' onClick={onClose}>
            <Image src={closeCircleFill} className='w-24 h-24' />
          </Button>
        </View>
      </View>

      {/* Container */}
      <View className={'pop-content'}>{children}</View>
    </Popup>
  );
};

export default CustomPopup;
