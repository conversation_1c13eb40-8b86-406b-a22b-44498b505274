import { memo, useState } from 'react';
import { View } from '@tarojs/components';
import Modal from '@/components/Modal';
import '../BatterySafetyNotice/index.less';

const BuyTicketNotice = () => {
  const [modalVisible, setModalVisible] = useState(false);
  return (
    <>
      <View
        className={'tips-item'}
        onClick={() => {
          setModalVisible(true);
        }}
      >
        购票须知
      </View>
      <Modal show={modalVisible} title={'购票须知'} onClose={() => setModalVisible(false)}>
        购票须知
      </Modal>
    </>
  );
};
export default memo(BuyTicketNotice);
