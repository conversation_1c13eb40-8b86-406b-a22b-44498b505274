.recommend-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 6px;
  .recommend-item {
    //width: 175px;
    width: 170px;
    height: 222px;
    //margin-bottom: 16px;
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    background: #fff;
    image {
      width: 170px;
      height: 160px;
      object-fit: cover;
    }
    .recommend-airline {
      width: 100%;
      box-sizing: border-box;
      padding: 12px 16px;
      color: #23242d;
      border-radius: 16px;
      .airline-name-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 6px;
        .price {
          margin-left: 12px;
          color: #124072;
        }
        .split-line {
          margin: 0 4px;
        }
      }
      .desc {
        font-size: 12px;
        color: #4f5170;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        .seat {
          display: inline-flex;
          align-items: center;
        }
      }
    }
  }
}
