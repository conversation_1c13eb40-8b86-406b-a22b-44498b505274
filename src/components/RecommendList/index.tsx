import { memo, useEffect, useState } from 'react';
import { Image, Text, View } from '@tarojs/components';
import { jaunt } from '@/utils/img';
import apis from '@/api';
import type { ShortRouteRecommendVO } from '@/api/servies/dkx';
import CustomEmpty from '@/components/CustomEmpty';
import './index.less';
import { SUCCESS_CODE } from '@/utils/constant';

interface RecommendListProps {
  type?: 'home' | 'jaunt' | 'lowAltitude';
}

const RecommendList = ({ type }: RecommendListProps) => {
  const [recommendData, setRecommendData] = useState<ShortRouteRecommendVO[]>([]);
  const [loading, setLoading] = useState(false);

  // 根据类型获取推荐数据
  const fetchRecommendData = async () => {
    if (!type) return;

    setLoading(true);

    if (type === 'jaunt') {
      // jaunt 类型调用短途航线推荐接口
      const { code, data }: any = await apis.shortRouteV10RecommendCreate();
      if (code === SUCCESS_CODE) {
        setRecommendData(data);
      }
    } else if (type === 'home') {
      // todo home 和 lowAltitude 类型暂时预留，使用空数据
      setRecommendData([]);
    } else if (type === 'lowAltitude') {
      setRecommendData([]);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchRecommendData();
  }, [type]);

  // 渲染加载状态
  if (loading) {
    return (
      <View className={'recommend-list'}>
        <CustomEmpty
          text='加载中...'
          description='正在获取推荐数据，请稍候'
          className='loading-state'
        />
      </View>
    );
  }

  // 如果没有数据，显示空状态
  if (recommendData.length === 0) {
    return (
      <View className={'recommend-list'}>
        <CustomEmpty text='暂无数据' />
      </View>
    );
  }

  return (
    <>
      <View className={'recommend-list'}>
        {recommendData.map((item, index) => {
          return (
            <View className={'recommend-item'} key={index}>
              {/*<View*/}
              {/*  className={'is-top'}*/}
              {/*  style={{*/}
              {/*    background: `url(${recommend}) no-repeat center center/cover`,*/}
              {/*  }}*/}
              {/*>*/}
              {/*  <View className={'top-txt'}>TOP</View>*/}
              {/*  <View className={'top-num'}>01</View>*/}
              {/*</View>*/}
              <Image src={item.imageUrl || jaunt} />
              <View className={'recommend-airline'}>
                <View className={'airline-name-box'}>
                  <Text className={'airline'}>
                    {item.depCity && item.arrCity ? `${item.depCity}-${item.arrCity}` : '航线信息'}
                  </Text>
                  <Text className={'price'}>{item.price ? `¥ ${item.price}` : '价格待定'}</Text>
                </View>
                <View className={'desc'}>
                  <Text>空客H135</Text>
                  <View className={'split-line'}></View>
                  <View className={'seat'}>5-6人</View>
                </View>
              </View>
            </View>
          );
        })}
      </View>
    </>
  );
};
export default memo(RecommendList);
