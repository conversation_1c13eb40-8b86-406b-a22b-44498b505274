.custom-tab {
  width: 100%;
  box-sizing: border-box;
  //box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

  .custom-tab-title-box {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    //overflow: hidden;
    margin-bottom: -1px;
    .custom-tab-title-item {
      background: #e8f0ff;
      color: #525b67;
      font-size: 14px;
      text-align: center;
      height: 36px;
      flex: 1;
      line-height: 36px;
      position: relative;
      border: none;
      max-width: 50%;
      &:first-child {
        border-radius: 12px 0 0 0;
      }
      &:last-child {
        border-radius: 0 12px 0 0;
      }
      &.active {
        height: 44px;
        position: relative;
        background: #fff;
        color: #2b2b32;
        font-weight: bold;
        border-radius: 12px 12px 0 0;
        &::after {
          content: '';
          position: absolute;
          bottom: 6px;
          left: calc(50% - 35px);
          width: 64px;
          height: 4px;
          border-radius: 12px;
          background: linear-gradient(90.6deg, rgba(18, 64, 114, 0) 6.12%, #124072 91.87%);
        }
      }
    }
  }
  .custom-tab-content {
    background: #fff;
    box-sizing: border-box;
    padding: 16px;
    border-radius: 0 0 12px 12px;
  }
}
