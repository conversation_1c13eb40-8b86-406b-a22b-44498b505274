import React, { memo, useEffect, useState } from "react";
import { Text, View } from "@tarojs/components";
import "./index.less";

interface CustomTabItemProps {
  label: string;
  value?: string | number;
  children: React.ReactNode;
  active?: boolean;
  onClick?: () => void;
}
interface CustomTabProps {
  defaultValue?: string | number;
  list: Array<CustomTabItemProps>;
  getData?: (value: string | number) => void;
  className?: string;
}

const CustomTab = ({
  list,
  defaultValue,
  getData,
  className,
}: CustomTabProps) => {
  const [activeVal, setActiveVal] = useState<string | number>();

  const handleClick = (item) => {
    setActiveVal(item?.value);
    getData?.(item?.value);
  };
  useEffect(() => {
    if (defaultValue) {
      setActiveVal(defaultValue);
    } else {
      list?.length > 0 && setActiveVal(list[0]?.value);
    }
  }, [defaultValue, list]);

  return (
    <>
      <View className={`custom-tab ${className}`}>
        <View className={"custom-tab-title-box"}>
          {list.map((item) => {
            return (
              <View
                className={`custom-tab-title-item ${
                  activeVal === item?.value ? "active" : ""
                }`}
                key={item?.value}
                onClick={() => handleClick(item)}
              >
                <Text className={"custom-tab-title-text"}>{item?.label}</Text>
              </View>
            );
          })}
        </View>
        <View className={"custom-tab-content"}>
          {list?.map((item) => item.value === activeVal && item.children)}
        </View>
      </View>
    </>
  );
};
export default memo(CustomTab);
