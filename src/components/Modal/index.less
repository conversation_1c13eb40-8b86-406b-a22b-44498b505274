.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s ease-in-out;
}

.modal-content {
  background-color: white;
  padding: 10px 20px 10px 20px;
  border-radius: 10px;
  width: 80%;
  max-width: 500px;
  max-height: 80vh;
  text-align: center;
  position: relative;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  height: 30px;
  line-height: 30px;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

.modal-body {
  max-height: calc(80vh - 90px);
  overflow-y: auto;
}

.modal-close-btn {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  color: #1890ff;
  border: none;
  border-radius: 5px;
}
