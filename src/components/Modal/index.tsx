import React, { useState, useCallback, useEffect } from "react";
import { View } from "@tarojs/components";
import "./index.less";

interface ModalProps {
  show: boolean;
  title?: string;
  children?: React.ReactNode;
  showCloseBtn?: boolean;
  onClose?: () => void;
  onOk?: () => void;
}

const Modal = ({
  show,
  title,
  children,
  onClose,
  showCloseBtn = true,
}: ModalProps) => {
  const [isVisible, setIsVisible] = useState(show);

  const handleClose = useCallback(() => {
    setIsVisible(false);
    onClose && onClose();
  }, [onClose]);

  useEffect(() => {
    setIsVisible(show);
  }, [show]);

  if (!isVisible) {
    return null;
  }

  return (
    <View className="modal-overlay">
      <View className="modal-content">
        {title && <View className="modal-title">{title}</View>}
        <View className="modal-body">{children}</View>
        {showCloseBtn && (
          <View className="modal-close-btn" onClick={handleClose}>
            关闭
          </View>
        )}
      </View>
    </View>
  );
};

export default Modal;
