import { memo, useEffect, useState } from "react";
import { View } from "@tarojs/components";
import "./index.less";
import { Location } from "@nutui/icons-react-taro";
import Taro from "@tarojs/taro";

const Position = () => {
  const [cityName, setCityName] = useState<string>("定位中...");

  const getLocation = () => {
    Taro.getLocation({
      type: "gcj02", // 使用国测局坐标系
      success: function (res) {
        console.log(res);
      },
      fail: function () {
        setCityName("定位失败");
      },
    });
  };
  useEffect(() => {
    getLocation();
  }, []);
  return (
    <>
      <View className="position-container">
        <View className="position-text">{cityName}</View>
        <Location width={"10px"} height={"14px"} color="#999" />
      </View>
    </>
  );
};
export default memo(Position);
