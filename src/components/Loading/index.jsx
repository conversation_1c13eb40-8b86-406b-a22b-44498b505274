import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { loadingPanda } from '../img';
import './index.less';

const Index = props => {

  const { visible, setVisible } = props;

  const countdownTimer = useRef();
  const [countdown, setCountdown] = useState(0);

  const handleCountdown = () => {
    if (countdown < 99) {
      countdownTimer.current = setTimeout(() => {
        const newCountdown = countdown + 10;
        setCountdown(newCountdown > 99 ? 99 : newCountdown);
      }, 50)
    } else {
      clearTimeout(countdownTimer.current);
    }
  }

  useEffect(() => {
    handleCountdown();
  }, [countdown]);


  return (
    <View className={`loading ${visible ? 'hide' : ''}`}>
      <View className='content'>
        <View className='img'>
          <Image className="img-c" src={loadingPanda} webp="webp" alt='' mode="widthFix" />
        </View>
        <View className='progress'>
          <View className='prospect' style={{ width: `${countdown}%` }}></View>
        </View>
        <View className='schedule' style={{ width: `${countdown}%` }}><Text>{countdown}%</Text></View>
      </View>
    </View>
  );
}

export default Index;