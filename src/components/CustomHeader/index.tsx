import { View } from '@tarojs/components';
import Taro, { getEnv, ENV_TYPE } from '@tarojs/taro';
import { ArrowLeftSmall } from '@nutui/icons-react-taro';
import './index.less';
import { goBack, getStatusBarHeight } from '@/utils';
import React from 'react';

// 默认头部参数
const DEFAULT_HEADER_PARAMS = {
  navBarHeight: 88,
  menuBottomHeight: 32,
  menuBottomTop: 48,
};

// 预计算头部参数
// 使用立即执行函数确保只计算一次
const CALCULATED_HEADER_PARAMS = (() => {
  try {
    // 如果不是微信环境，直接返回默认值
    if (typeof getEnv === 'function' && getEnv() !== ENV_TYPE.WEAPP) {
      return DEFAULT_HEADER_PARAMS;
    }

    // 初始化默认值
    let menuBottomHeight = DEFAULT_HEADER_PARAMS.menuBottomHeight;
    let menuBottomTop = DEFAULT_HEADER_PARAMS.menuBottomTop;
    let statusBarHeight = 20;

    // 安全地获取胶囊信息
    try {
      if (Taro && typeof Taro.getMenuButtonBoundingClientRect === 'function') {
        const menuButtonObject = Taro.getMenuButtonBoundingClientRect();
        if (menuButtonObject && typeof menuButtonObject === 'object') {
          if (menuButtonObject.height > 0) {
            menuBottomHeight = menuButtonObject.height;
          }
          if (menuButtonObject.top > 0) {
            menuBottomTop = menuButtonObject.top;
          }
        }
      }
    } catch (e) {
      console.warn('获取胶囊信息失败');
    }

    // 使用新的工具函数获取状态栏高度
    statusBarHeight = getStatusBarHeight();

    // 计算导航栏高度
    const navBarHeight = statusBarHeight + menuBottomHeight + (menuBottomTop - statusBarHeight) * 2;

    // 验证计算结果
    if (isNaN(navBarHeight) || navBarHeight <= 0 || navBarHeight > 200) {
      return DEFAULT_HEADER_PARAMS;
    }

    return {
      navBarHeight,
      menuBottomHeight,
      menuBottomTop,
    };
  } catch (error) {
    return DEFAULT_HEADER_PARAMS;
  }
})();

// 安全地获取头部参数
export const calcHeaderParameter = () => {
  return CALCULATED_HEADER_PARAMS;
};

interface CustomHeaderProps {
  title?: string;
  showBackButton?: boolean;
  customElement?: React.ReactNode;
  onBack?: () => void;
  backNum?: number;
  bgColor?: string;
  titleColor?: string;
  backIconColor?: string;
}

const CustomHeader = ({
  title,
  showBackButton,
  customElement,
  onBack,
  backNum = 1,
  bgColor,
  titleColor,
  backIconColor = '#fff',
}: CustomHeaderProps) => {
  const { navBarHeight, menuBottomHeight, menuBottomTop } = calcHeaderParameter();

  return (
    <View
      className='custom-header'
      style={{
        height: navBarHeight + 'px',
        background: bgColor ? bgColor : '#1663F8',
      }}
    >
      {showBackButton && (
        <View
          className='go-back'
          onClick={() => {
            onBack ? onBack() : goBack(backNum);
          }}
          style={{
            width: menuBottomHeight + 'px',
            height: menuBottomHeight + 'px',
            top: menuBottomTop + 'px',
          }}
        >
          <ArrowLeftSmall color={backIconColor} width='20px' />
        </View>
      )}
      {customElement && (
        <View
          className='custom-element'
          style={{
            top: menuBottomTop - 5 + 'px',
            left: showBackButton ? menuBottomHeight + 16 + 'px' : 16 + 'px',
          }}
        >
          {customElement}
        </View>
      )}

      <View
        className='page-title'
        style={{
          height: menuBottomHeight + 'px',
          lineHeight: menuBottomHeight + 'px',
          top: menuBottomTop + 'px',
          color: titleColor ? titleColor : '#fff',
        }}
      >
        {title}
      </View>
    </View>
  );
};

export default CustomHeader;
