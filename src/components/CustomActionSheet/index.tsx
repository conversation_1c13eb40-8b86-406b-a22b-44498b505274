/*
 * @Author: lengkj <EMAIL>
 * @Date: 2025-04-15 09:16:39
 * @LastEditors: lengkj <EMAIL>
 * @LastEditTime: 2025-04-24 16:11:53
 * @FilePath: /tbs/src/components/CustomActionSheet/index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ActionSheet, BaseActionSheet } from '@nutui/nutui-react-taro';
import { Button, View, Image } from '@tarojs/components';

import { closeCircleFill } from '@/utils/img';

export interface ICustomActionSheetProps extends BaseActionSheet {
  onConfirm?: <T>(val: T) => T | void;
  onCancel?: () => void;
}

const CustomActionSheet = ({
  onCancel,
  onConfirm,
  title,
  children,
  ...rest
}: ICustomActionSheetProps) => {
  return (
    <ActionSheet onCancel={onCancel} {...rest}>
      <View className='p-16'>
        <View className='flex justify-between items-center border-b-1 border-E6EAF0 pb-16'>
          <View className='text-20 font-semibold'>{title}</View>

          <View>
            <Button className='p-0 bg-transparent' onClick={onCancel}>
              <Image src={closeCircleFill} className='w-24 h-24' />
            </Button>
          </View>
        </View>

        <>{children}</>

        {onConfirm && (
          <Button
            className='mt-24 w-full h-48 leading-48 rounded-12 bg-primary text-16 text-white font-semibold'
            onClick={onConfirm}
          >
            确定
          </Button>
        )}
      </View>
    </ActionSheet>
  );
};

export default CustomActionSheet;
