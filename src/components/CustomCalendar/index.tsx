import { useRef } from 'react';

import CustomActionSheet, { ICustomActionSheetProps } from '../CustomActionSheet';
import { CalendarCard, BaseCalendarCard, CalendarCardDay } from '@nutui/nutui-react-taro';

import './index.less';
import { View } from '@tarojs/components';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@/utils/constant';

export interface ICustomCalendarProps {
  customActionSheetProps?: ICustomActionSheetProps;
  calendarCardProps?: Partial<BaseCalendarCard>;
}

const CustomCalendar = ({ customActionSheetProps, calendarCardProps }: ICustomCalendarProps) => {
  const valDate = useRef('');

  const { onConfirm, ...restCustomActionSheetProps } =
    customActionSheetProps as ICustomActionSheetProps;

  return (
    <View className='custom-calendar'>
      <CustomActionSheet
        onConfirm={() => {
          onConfirm?.(valDate.current);
        }}
        {...restCustomActionSheetProps}
      >
        <CalendarCard
          onChange={(val: any) => {
            valDate.current = dayjs(val).format(DATE_FORMAT);
          }}
          disableDay={(day: CalendarCardDay) => {
            // 禁用已经过去的日期
            const dateStr = day.year + '-' + day.month + '-' + day.date;
            const today = dayjs().startOf('day');
            return dayjs(dateStr).isBefore(today);
          }}
          {...calendarCardProps}
        />
      </CustomActionSheet>
    </View>
  );
};

export default CustomCalendar;
