import cx from "classnames";

import { View } from "@tarojs/components";

interface IBottomCtrProps {
  className?: string;
  children?: React.ReactNode;
}

const BottomCtr = ({ children, className }: IBottomCtrProps) => {
  return (
    <View>
      {/* 为底部导航栏添加空白填充 */}
      <View className="h-50 pt-6 safe-pb box-content"></View>

      {/* 底部导航栏 */}
      <View className="w-full px-16 pt-10 safe-pb bg-white border-t-1 border-E6EAF0 fixed bottom-0 left-0 z-10">
        <View className={cx("h-50", className)}>{children}</View>
      </View>
    </View>
  );
};

export default BottomCtr;
