import { memo, useState } from "react";
import { Text, View } from "@tarojs/components";
import "./index.less";
import Modal from "@/components/Modal";
import { Check } from "@nutui/icons-react-taro";

interface LoginProtocolProps {
  getData: (checked: boolean) => void;
}

const userProtocol = "用户协议";

const privacyPolicy = "隐私政策";

const LoginProtocol = ({ getData }: LoginProtocolProps) => {
  const [checked, setChecked] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [openType, setOpenType] = useState("");

  const handleShowModal = (type: string) => {
    setOpenType(type);
    setShowModal(!showModal);
  };

  const handleChange = () => {
    setChecked(!checked);
    getData(!checked);
  };

  return (
    <>
      <View className={"login-protocol-checkbox"}>
        <View className={`checkbox-box`} onClick={handleChange}>
          {checked && <Check width="14px" color="#1466e1" />}
        </View>
        <View className={"checkbox-text"}>
          我已阅读并同意遵守低空行平台
          <Text onClick={() => handleShowModal("privacyPolicy")}>
            《隐私政策》
          </Text>
          <Text onClick={() => handleShowModal("userProtocol")}>
            《用户协议》
          </Text>
        </View>
      </View>
      <Modal
        show={showModal}
        title={openType === "userProtocol" ? "用户协议" : "隐私政策"}
        onClose={() => {
          setShowModal(false);
        }}
      >
        {openType === "userProtocol" ? userProtocol : privacyPolicy}
      </Modal>
    </>
  );
};
export default memo(LoginProtocol);
