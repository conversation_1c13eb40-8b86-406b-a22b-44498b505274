import { memo, useEffect, useState } from 'react';
import { Button, Image, View, Text } from '@tarojs/components';
import { ArrowTransfer, Plus, Minus } from '@nutui/icons-react-taro';
import './index.less';
import { calendarIcon } from '@/utils/img';
import CustomCalendar from '@/components/CustomCalendar';
import CustomTag from '@/components/CustomTag';
import dayjs from 'dayjs';
import { DATE_FORMAT, SUCCESS_CODE } from '@/utils/constant';
import api from '@/api';
import { Picker } from '@nutui/nutui-react-taro';
import { FlightSegmentProps, SearchAirlineProps } from '@/types/flight';

interface OptionProps {
  text?: string;
  value?: string;
  label?: string;
}

const SearchAirline = ({ type = 'oneWay', getData }: SearchAirlineProps) => {
  const [options, setOptions] = useState<OptionProps[]>([]);
  const [dateShow, setDateShow] = useState(''); // 日期选择框是否显示
  const [currentSegment, setCurrentSegment] = useState(0); // 当前编辑的航段

  // 统一的航班段数据管理
  const [flightSegments, setFlightSegments] = useState<FlightSegmentProps[]>(() => {
    const defaultSegment: FlightSegmentProps = {
      arrName: '',
      arrCode: '',
      depName: '',
      depCode: '',
      flightDate: dayjs().format(DATE_FORMAT),
    };

    switch (type) {
      case 'roundTrip':
        return [
          { ...defaultSegment },
          { ...defaultSegment, flightDate: dayjs().add(2, 'day').format(DATE_FORMAT) },
        ];
      case 'multipass':
        return [
          { ...defaultSegment },
          { ...defaultSegment, flightDate: dayjs().add(1, 'day').format(DATE_FORMAT) },
        ];
      default:
        return [{ ...defaultSegment }];
    }
  });
  /**
   * 获取短途航线城市数据
   */
  const getShortCity = async () => {
    const { code, data } = await api.shortRouteV10CityCreate();
    if (code === SUCCESS_CODE && data) {
      let arr: any[] = [];
      data?.titleLabels?.[0]?.shortRouteCityList?.map(item => {
        arr.push({
          text: item.airportName,
          value: item.airportCode,
          label: item.airportName,
        });
      });
      setOptions(arr);

      // 更新统一的航班段数据
      if (data?.depAirportName && data?.arrAirportName) {
        setFlightSegments(prev =>
          prev.map((segment, index) => {
            if (index === 0) {
              return {
                ...segment,
                depName: data.depAirportName,
                depCode: data.depAirportCode,
                arrName: data.arrAirportName,
                arrCode: data.arrAirportCode,
              };
            }
            // 往返航线的返程段，交换出发和到达
            if (type === 'roundTrip' && index === 1) {
              return {
                ...segment,
                depName: data.arrAirportName,
                depCode: data.arrAirportCode,
                arrName: data.depAirportName,
                arrCode: data.depAirportCode,
              };
            }
            return segment;
          })
        );
      }
    }
  };

  useEffect(() => {
    getShortCity();
  }, []);
  // 渲染地址选择器
  const renderAddress = (segmentIndex: number, isArrival: boolean) => {
    const [visible, setVisible] = useState(false);

    // 获取显示的值
    const segment = flightSegments[segmentIndex];
    const displayVal = isArrival ? segment?.arrName : segment?.depName;

    // 处理选择确认
    const onConfirm = (selectedOptions: any[]) => {
      const selectedOption = selectedOptions?.[0];

      if (selectedOption) {
        setFlightSegments(prev => {
          const newSegments = [...prev];
          if (isArrival) {
            newSegments[segmentIndex].arrName = selectedOption.text || selectedOption.label;
            newSegments[segmentIndex].arrCode = selectedOption.value;
          } else {
            newSegments[segmentIndex].depName = selectedOption.text || selectedOption.label;
            newSegments[segmentIndex].depCode = selectedOption.value;
          }
          return newSegments;
        });
      }
      setVisible(false);
    };

    // 获取当前选中的值（用于Picker的defaultValue）
    const getCurrentValue = () => {
      if (!displayVal || options.length === 0) return [];
      const foundOption = options.find(option => option.label === displayVal);
      return foundOption && foundOption.value ? [foundOption.value] : [];
    };

    return (
      <>
        <View
          className={`city-name ${displayVal ? 'active' : ''}`}
          onClick={() => setVisible(true)}
        >
          {displayVal || '请选择'}
        </View>
        <Picker
          visible={visible}
          title='选择出发/到达城市'
          options={[options as any]}
          defaultValue={getCurrentValue()}
          onConfirm={onConfirm}
          onCancel={() => setVisible(false)}
        />
      </>
    );
  };
  // 交换出发城市和到达城市
  const exchangeCity = (segmentIndex: number) => {
    setFlightSegments(prev => {
      const newSegments = [...prev];
      const segment = newSegments[segmentIndex];
      const tempName = segment.depName;
      const tempCode = segment.depCode;

      segment.depName = segment.arrName;
      segment.depCode = segment.arrCode;
      segment.arrName = tempName;
      segment.arrCode = tempCode;

      return newSegments;
    });
  };

  // 添加航段（仅多程航线）
  const addSegment = () => {
    if (type === 'multipass' && flightSegments.length < 5) {
      setFlightSegments(prev => [
        ...prev,
        {
          arrName: '',
          arrCode: '',
          depName: '',
          depCode: '',
          flightDate: dayjs().format(DATE_FORMAT),
        },
      ]);
    }
  };

  // 删除航段（仅多程航线）
  const removeSegment = (index: number) => {
    if (type === 'multipass' && flightSegments.length > 2) {
      setFlightSegments(prev => prev.filter((_, i) => i !== index));
    }
  };

  // 更新日期
  const updateDate = (date: string, segmentIndex: number) => {
    setFlightSegments(prev => {
      const newSegments = [...prev];
      newSegments[segmentIndex].flightDate = date;
      return newSegments;
    });
  };

  // 准备提交数据
  const prepareData = (): FlightSegmentProps[] => {
    // 过滤掉空的航段数据
    return flightSegments.filter(
      segment =>
        segment.depName &&
        segment.arrName &&
        segment.depCode &&
        segment.arrCode &&
        segment.flightDate
    );
  };

  // 渲染城市选择框
  const renderCitySelector = (segmentIndex: number) => {
    return (
      <View className={`search-item`}>
        <View className={'city-select dep-city'}>
          <View className={'desc'}>出发城市</View>
          {renderAddress(segmentIndex, false)}
        </View>
        <View className={'exchange-icon'} onClick={() => exchangeCity(segmentIndex)}>
          <ArrowTransfer width={12} height={12} color={'#B5BACA'} />
        </View>
        <View className={'city-select arr-city'}>
          <View className={'desc'}>到达城市</View>
          {renderAddress(segmentIndex, true)}
        </View>
      </View>
    );
  };

  // 渲染日期选择框
  const renderDateSelector = (segmentIndex: number) => {
    const segment = flightSegments[segmentIndex];
    const dateValue = segment?.flightDate;

    const handleDateClick = () => {
      setCurrentSegment(segmentIndex);
      setDateShow(type || 'oneWay');
    };

    return (
      <View className={'search-item time-select-box'}>
        <View className={'city-select dep-city'} onClick={handleDateClick}>
          <View className={'desc'}>航班日期</View>
          <View className={'val'}>{dateValue}</View>
        </View>
        <View className={'city-select arr-city'}>
          <Image src={calendarIcon} className={'calendar-icon'} />
        </View>
      </View>
    );
  };

  // 渲染往返航线的日期选择框
  const renderRoundTripDateSelector = () => {
    const goSegment = flightSegments[0];
    const returnSegment = flightSegments[1];

    // 计算日期间隔
    const calculateDateInterval = () => {
      try {
        const depDate = dayjs(goSegment?.flightDate);
        const retDate = dayjs(returnSegment?.flightDate);
        if (depDate.isValid() && retDate.isValid()) {
          const diffDays = retDate.diff(depDate, 'day');
          return `${Math.abs(diffDays)}天`;
        }
      } catch (e) {}
      return '0天';
    };

    return (
      <View className={'search-item time-select-box'}>
        <View
          className={'city-select dep-city'}
          onClick={() => {
            setCurrentSegment(0);
            setDateShow('roundTrip');
          }}
        >
          <View className={'desc'}>去程</View>
          <View className={'val'}>{goSegment?.flightDate}</View>
        </View>
        <View className={'interval-time'}>{calculateDateInterval()}</View>
        <View
          className={'city-select arr-city'}
          onClick={() => {
            setCurrentSegment(1);
            setDateShow('roundTrip');
          }}
        >
          <View className={'desc'}>返程</View>
          <View className={'val'}>{returnSegment?.flightDate}</View>
        </View>
      </View>
    );
  };

  return (
    <>
      <View className={'airline-search-box'}>
        {/* 单程和往返航线 */}
        {(type === 'oneWay' || type === 'roundTrip') && (
          <>
            {renderCitySelector(0)}
            {type === 'oneWay' && renderDateSelector(0)}
            {type === 'roundTrip' && renderRoundTripDateSelector()}
          </>
        )}

        {/* 多程航线 */}
        {type === 'multipass' && (
          <>
            {flightSegments.map((_item, index) => (
              <View className={'card-box multipass-segment'} key={index}>
                <View className={'segment-header'}>
                  <Text className={'segment-number'}>航段 {index + 1}</Text>
                  {flightSegments.length > 2 && (
                    <Button className={'remove-segment-btn'} onClick={() => removeSegment(index)}>
                      <Minus width={16} height={16} color={'#B5BACA'} />
                    </Button>
                  )}
                </View>
                {renderCitySelector(index)}
                {renderDateSelector(index)}
              </View>
            ))}
            {flightSegments.length < 5 && (
              <CustomTag
                bgColor={'#F7F8FA'}
                color={'#124072'}
                onClick={() => addSegment()}
                size={'large'}
              >
                <Plus width={12} height={12} color={'#124072'} />
                <Text className={'add-segment-text'}>新增行程</Text>
              </CustomTag>
            )}
          </>
        )}

        <Button
          className={'search-btn'}
          onClick={() => {
            const segments = prepareData();
            getData?.(segments);
          }}
        >
          查询价格
        </Button>
      </View>

      <CustomCalendar
        customActionSheetProps={{
          visible: !!dateShow,
          onCancel: () => setDateShow(''),
          onConfirm: val => {
            if (!val) return;
            const selectedDate = val as string;
            updateDate(selectedDate, currentSegment);
            setDateShow('');
          },
        }}
      />
    </>
  );
};
export default memo(SearchAirline);
