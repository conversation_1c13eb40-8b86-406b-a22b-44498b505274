import React, { memo } from 'react';
import './index.less';
import { View } from '@tarojs/components';

const colorTypes: any = {
  default: {
    backgroundColor: 'rgba(0, 0, 0, 0.08)',
    color: '#4F5170',
  },
  success: {
    backgroundColor: '#F1FFF5',
    color: '#24A743',
  },
  warning: {
    backgroundColor: '#FAF2F0',
    color: '#F84F2A',
  },
  info: {
    backgroundColor: '#F3F5FF',
    color: '#3350EE',
  },
  normal: {
    backgroundColor: '#F2F8FF',
    color: '#124072',
  },
  gray: {
    backgroundColor: '#F2F6FC',
    color: '#333',
  },
  blue: {
    backgroundColor: '#124072',
    color: '#fff',
  },
  ghost: {
    backgroundColor: 'transparent',
    color: '#4F5170',
    border: '1px solid #9AA2CA',
  },
  primary: {
    backgroundColor: '#1663F8',
    color: '#fff',
  },
};
const sizeTypes = {
  large: {
    borderRadius: '8px',
    fontSize: '14px',
    padding: '7px 20px',
  },
  medium: {
    borderRadius: '8px',
    padding: '6px 8px',
  },
  small: {
    borderRadius: '4px',
    padding: '2px 4px',
  },
};

interface CustomTagProps {
  type?:
    | 'default'
    | 'success'
    | 'warning'
    | 'info'
    | 'normal'
    | 'gray'
    | 'blue'
    | 'ghost'
    | 'primary'; //类型
  children?: React.ReactNode | string;
  isBold?: boolean; //文字是否加粗
  color?: string; // 自定义颜色
  bgColor?: string; //自定义背景色
  onClick?: () => void; //点击事件
  size?: 'large' | 'medium' | 'small'; //尺寸
}
const CustomTag = ({
  type = 'default',
  children,
  isBold = false,
  color,
  bgColor,
  onClick,
  size = 'small',
}: CustomTagProps) => {
  return (
    <View
      style={{
        fontWeight: isBold ? 'bold' : 'normal',
        color: color ? color : colorTypes[type]?.color,
        backgroundColor: bgColor ? bgColor : colorTypes[type]?.backgroundColor,
        border: colorTypes[type]?.border,
        ...sizeTypes[size],
      }}
      className={'custom-tag'}
      onClick={onClick}
    >
      {children}
    </View>
  );
};
export default memo(CustomTag);
