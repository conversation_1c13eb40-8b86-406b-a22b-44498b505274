import { View } from "@tarojs/components";
import CustomCalendar, {
  ICustomCalendarProps,
} from "@/components/CustomCalendar";
import { CalendarCardDay } from "@nutui/nutui-react-taro";

import "./index.less";

interface IPriceCalenderProps extends ICustomCalendarProps {}

const PriceCalendar = ({
  customActionSheetProps,
  calendarCardProps,
}: IPriceCalenderProps) => {
  return (
    <View className="price-calendar">
      <CustomCalendar
        customActionSheetProps={{
          ...customActionSheetProps,
        }}
        calendarCardProps={{
          renderDay: (day: CalendarCardDay) => {
            return <View className="day-view">{day.date}</View>;
          },
          renderDayBottom: (day: CalendarCardDay) => {
            // console.log(day, "day");

            return <View className="day-bottom-view">￥1980</View>;
          },
          ...calendarCardProps,
        }}
      />
    </View>
  );
};

export default PriceCalendar;
