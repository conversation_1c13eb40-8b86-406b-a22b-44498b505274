@import "../../styles/variables.less";

.token-status {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px;

  .token-status-header {
    font-size: 16px;
    font-weight: bold;
    color: @color-text-primary;
    margin-bottom: 16px;
    text-align: center;
  }

  .token-info {
    margin-bottom: 16px;

    .status-item {
      padding: 8px 12px;
      margin-bottom: 8px;
      border-radius: 4px;
      font-size: 14px;

      &.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      &.warning {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }

      &.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
    }
  }

  .token-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .action-btn {
      height: 36px;
      border-radius: 4px;
      font-size: 14px;
      border: none;

      &.refresh-btn {
        background-color: @color-primary;
        color: white;

        &[disabled] {
          background-color: #ccc;
          color: #666;
        }
      }

      &.clear-btn {
        background-color: #dc3545;
        color: white;
      }

      &.update-btn {
        background-color: #28a745;
        color: white;
      }
    }
  }
}
