import { memo, useState, useEffect } from 'react';
import { View, Text, Button } from '@tarojs/components';
import { checkTokenStatus, refreshToken, _localStorage } from '@/utils';
import './index.less';

const TokenStatus = () => {
  const [tokenInfo, setTokenInfo] = useState({
    hasToken: false,
    isExpired: false,
    needRefresh: false,
    refreshExpired: false
  });
  const [isRefreshing, setIsRefreshing] = useState(false);

  const updateTokenInfo = () => {
    const status = checkTokenStatus();
    setTokenInfo(status);
  };

  const handleRefreshToken = async () => {
    setIsRefreshing(true);
    try {
      const success = await refreshToken();
      if (success) {
        updateTokenInfo();
      }
    } catch (error) {
      console.error('刷新token失败:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const clearTokens = () => {
    _localStorage.removeItem('token');
    _localStorage.removeItem('refreshToken');
    _localStorage.removeItem('uid');
    _localStorage.removeItem('accessTokenTimeOut');
    _localStorage.removeItem('refreshTokenTimeOut');
    updateTokenInfo();
  };

  useEffect(() => {
    updateTokenInfo();
    // 每30秒检查一次token状态
    const interval = setInterval(updateTokenInfo, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <View className="token-status">
      <View className="token-status-header">Token状态监控</View>
      
      <View className="token-info">
        <View className={`status-item ${tokenInfo.hasToken ? 'success' : 'error'}`}>
          <Text>Token存在: {tokenInfo.hasToken ? '是' : '否'}</Text>
        </View>
        
        <View className={`status-item ${tokenInfo.isExpired ? 'error' : 'success'}`}>
          <Text>Token过期: {tokenInfo.isExpired ? '是' : '否'}</Text>
        </View>
        
        <View className={`status-item ${tokenInfo.needRefresh ? 'warning' : 'success'}`}>
          <Text>需要刷新: {tokenInfo.needRefresh ? '是' : '否'}</Text>
        </View>
        
        <View className={`status-item ${tokenInfo.refreshExpired ? 'error' : 'success'}`}>
          <Text>RefreshToken过期: {tokenInfo.refreshExpired ? '是' : '否'}</Text>
        </View>
      </View>

      <View className="token-actions">
        <Button 
          className="action-btn refresh-btn" 
          onClick={handleRefreshToken}
          disabled={isRefreshing || !tokenInfo.hasToken}
        >
          {isRefreshing ? '刷新中...' : '手动刷新Token'}
        </Button>
        
        <Button 
          className="action-btn clear-btn" 
          onClick={clearTokens}
        >
          清空Token
        </Button>
        
        <Button 
          className="action-btn update-btn" 
          onClick={updateTokenInfo}
        >
          更新状态
        </Button>
      </View>
    </View>
  );
};

export default memo(TokenStatus);
