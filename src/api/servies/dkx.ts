/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** 通用 API 返回结果 */
export interface CommonApiResultVoid {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: any;
  success?: boolean;
}

/** 低空产品套餐架次查询 */
export interface TravelPackageSortieListDTO {
  /**
   * 产品ID
   * @example 30192
   */
  productId: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListTravelSortieListVo {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: TravelSortieListVo[];
  success?: boolean;
}

/** 低空架次查询 */
export interface TravelSortieListVo {
  /**
   * 起飞时间
   * @example "8:10"
   */
  depPlanTime?: string;
  /**
   * 到达时间
   * @example "10:10"
   */
  arrPlanTime?: string;
  /**
   * 座位数
   * @format int64
   * @example 50
   */
  count?: number;
  /**
   * 已售座位数
   * @format int64
   * @example 0
   */
  countSale?: number;
  /**
   * 架次模板名称
   * @example "名称"
   */
  sortiesName?: string;
  /**
   * 架次唯一编号
   * @example *********
   */
  sortiesNo?: string;
}

/** 低空退票价格查询 */
export interface TravelRefundPriceDTO {
  /**
   * 订单号
   * @example "B201073432901931737"
   */
  orderNo: string;
  /** 客票号 */
  ticketNos?: string[];
}

/** 通用 API 返回结果 */
export interface CommonApiResultTravelRefundPriceVo {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: TravelRefundPriceVo;
  success?: boolean;
}

/** 低空退票价格查询 */
export interface TravelRefundPriceVo {
  /** 客票退票信息 */
  refundInfoList?: TravelRefundTicketRefundInfoVo[];
  /** 订单编号 */
  orderNo: string;
  /**
   * 退款价格总计
   * @format int64
   */
  refundPrice: number;
  /**
   * 退单手续费总
   * @format int64
   * @example 200
   */
  refundTotalProcedureFee: number;
}

/** 低空退票价格旅客详情查询 */
export interface TravelRefundTicketRefundInfoVo {
  /**
   * 旅客姓名
   * @example "信息"
   */
  passengerName: string;
  /**
   * 客票号
   * @example "TP20220238340924192"
   */
  ticketNo: string;
  /**
   * 客票价格
   * @format int64
   * @example 100
   */
  ticketPrice: number;
  /**
   * 退票价格
   * @format int64
   * @example 50
   */
  refundPrice: number;
  /**
   * 退票手续费
   * @format int64
   * @example 50
   */
  refundProcedureFee: number;
}

/** 低空退票价格查询 */
export interface TravelRefundCreateDTO {
  /**
   * 订单号
   * @example "B201073432901931737"
   */
  orderNo: string;
  /** 客票号 */
  ticketNos?: string[];
  /**
   * 退票类型,默认0 自愿退 , 1 非自愿退款申请
   * @format int32
   * @example 1
   */
  type: number;
  /**
   * 退票原因，20字符以内,非自愿时必填
   * @example "疫情原因"
   */
  refundReason?: string;
  /**
   * 申请退款价格
   * @format int64
   * @example 100
   */
  refundPrice: number;
}

/** 通用 API 返回结果 */
export interface CommonApiResultTravelRefundOrderCreateVo {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: TravelRefundOrderCreateVo;
  success?: boolean;
}

/** 低空退票订单 */
export interface TravelRefundOrderCreateVo {
  /**
   * 订单编号
   * @example "B20202319201241912"
   */
  orderNo: string;
  /**
   * 退单编号
   * @example "R20120231292119313"
   */
  refundNo: string;
  /**
   * 退单状态 0、申请中 1、退票成功 -1、退票失败
   * @format int32
   * @example "R20120231292119313"
   */
  refundStatus: number;
  /**
   * 退单退款总价格
   * @format int64
   * @example 200
   */
  refundTotalPrice: number;
  /**
   * 退单申请时间 yyyy-MM-dd HH:mm:ss
   * @format date-time
   * @example "2021-09-08 17:19:49"
   */
  refundApplyTime: string;
  /** 客票退票信息 */
  refundInfoList?: TravelRefundTicketRefundInfoVo[];
}

/** 低空退票确认 */
export interface TravelRefundConfirmDTO {
  /**
   * 退单号(创建退单时返回 20位)
   * @example "R2022041821832399"
   */
  refundNo: string;
  /**
   * 确认结果代码, 1、退票成功 -1拒绝退票
   * @example 1
   */
  confirmResultCode: string;
  /**
   * 确认结果信息 confirmResultCode=-1 时必有
   * @example "申请理由与实际情况不符"
   */
  confirmResultMessage?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultTravelRefundOrderVo {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: TravelRefundOrderVo;
  success?: boolean;
}

/** 低空退票订单 */
export interface TravelRefundOrderVo {
  /**
   * 退单编号
   * @example "B20202319201241912"
   */
  refundNo: string;
  /**
   * 订单编号
   * @example "B20202319201241912"
   */
  orderNo: string;
  /**
   * 退单状态 0 未退款 1退款成功
   * @format int32
   * @example 0
   */
  refundStatus: number;
  /**
   * 退票类型,默认0 自愿退 , 1 非自愿退款申请
   * @format int32
   * @example 1
   */
  refundType: number;
  /**
   * 退款价格
   * @format int64
   * @example 111
   */
  refundPrice: number;
  /**
   * 申请人代码
   * @example 111
   */
  applyAgentCode: string;
  /**
   * 申请人
   * @example 111
   */
  applyOperater: string;
  /**
   * 申请时间
   * @format date-time
   * @example 111
   */
  applyTime: string;
  /**
   * 退款时间
   * @format date-time
   * @example 111
   */
  refundTime: string;
  /**
   * 备注
   * @example 111
   */
  remarks: string;
}

/** 低空产品须知 产品介绍 查询 */
export interface TravelProductNoDTO {
  /**
   * 产品编号
   * @example 10
   */
  productNo: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListTravelProductPictureVo {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: TravelProductPictureVo[];
  success?: boolean;
}

/** 低空产品图文信息查询 */
export interface TravelProductPictureVo {
  /**
   * 图片类型;0图文介绍、1购买须知、2产品首页图片、3产品详情图片
   * @format int64
   */
  pictureType?: number;
  /** 图片地址 */
  picturePath?: string;
  /**
   * 级别 排序
   * @format int64
   */
  pictureLevel?: number;
  /** 产品编号 */
  productNo?: string;
  /** 图片名 */
  pictureName?: string;
}

/** 低空产品列表查询 */
export interface TravelProductListDTO {
  /**
   *  产品类型 1 跳伞 2 滑翔机 3热气球 4 低空游览
   * @format int32
   * @example 1
   */
  type: number;
  /**
   * 产品基地代码
   * @example "CKG"
   */
  areaCode?: string;
}

/** 基地信息 */
export interface Area {
  /**
   * 名称
   * @example "基地名称"
   */
  areaName?: string;
  /**
   * 代码
   * @example "CKG"
   */
  areaCode?: string;
  /**
   * 基地备注
   * @example "基地备注"
   */
  remark?: string;
  /**
   * 地址
   * @example "重庆市渝北区木星科技大厦"
   */
  areaAddress?: string;
  /**
   * 经度
   * @example "116°E"
   */
  longitude?: string;
  /**
   * 纬度
   * @example "40°N"
   */
  latitude?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultPageResultListTravelProductListVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: PageResultListTravelProductListVO;
  success?: boolean;
}

/** 产品标签 */
export interface Labeling {
  /**
   * 标签名
   * @example "标签名"
   */
  labelingName?: string;
  /**
   * 标签备注
   * @example "标签备注"
   */
  labelingRemark?: string;
}

/** 分页返回对象 */
export interface PageResultListTravelProductListVO {
  /** 当前页记录 */
  rows?: TravelProductListVO[][];
  /**
   * 总记录数
   * @format int64
   */
  total?: number;
  /**
   * 当前页
   * @format int32
   */
  pageNum?: number;
  /**
   * 总页数
   * @format int32
   */
  pages?: number;
  /**
   * 每页多少数据
   * @format int32
   */
  pageSize?: number;
}

/** 低空产品查询 */
export interface TravelProductListVO {
  /** 产品名称 */
  productName?: string;
  /** 产品说明 */
  description?: string;
  /**
   * 产品类型 1 跳伞 2 滑翔机 3热气球 4 低空游览
   * @format int32
   */
  productType?: number;
  /** 产品航班号 */
  flightNo?: string;
  /** 产品航线 */
  route?: string;
  /** 高度 */
  skyHeight?: string;
  /** 所属基地CODE */
  areaCode?: string;
  /** 所属公司CODE */
  companyCode?: string;
  /**
   * 销售状态1可售 0不可售
   * @format int32
   */
  saleStatus?: number;
  /** 起飞信息 基地CODE */
  depAreaCode?: string;
  /** 到达信息 基地CODE */
  arrAreaCode?: string;
  /** 产品编号，20位 */
  productNo?: string;
  /** 起飞机场名 */
  depAreaName?: string;
  /** 到达机场名 */
  arrAreaName?: string;
  /**
   * 开始时间
   * @example "08:10"
   */
  openingTime?: string;
  /** 标签信息 */
  labeling?: Labeling[];
  /** 基地信息 */
  area?: Area;
  /** 产品首页图片 */
  homepagePicture?: string;
  /** 产品详情图片 */
  detailPicture?: string;
  /** 最低价格 */
  minPrice?: number;
}

/** 低空产品套餐查询 */
export interface TravelPackageListDTO {
  /**
   * 产品编号
   * @example 10
   */
  productNo: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListTravelPackageListVo {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: TravelPackageListVo[];
  success?: boolean;
}

/** 低空套餐查询 */
export interface TravelPackageListVo {
  /** 套餐名称 */
  packageName?: string;
  /** 套餐说明 */
  description?: string;
  /** 退票客规，0-24-100,24小时以外（含）申请扣除0%的手续费，24小时以内扣除100% */
  refundRule?: string;
  /** 退签客规，0-24-100,24小时以外（含）申请扣除0%的手续费，24小时以内扣除100% */
  rebookRule?: string;
  /** @format int32 */
  saleStatus?: number;
  /** 预付款 */
  advancePaymentPrice?: string;
  /** 总价 */
  totalPaymentPrice?: string;
  /** 摄像类型 */
  cameraType?: string;
  /** 套餐唯一编号 */
  packageNo?: string;
}

/** 产品套餐日历查询 */
export interface TravelPackageCalendarListDTO {
  /**
   * 套餐编号
   * @example 30192
   */
  packageNo?: string;
  /**
   * 产品编号
   * @example 30192
   */
  productNo?: string;
  /**
   * 开始日期
   * @example "2020-07-08"
   */
  startDate?: string;
  /**
   * 结束日期
   * @example "2020-07-08"
   */
  endDate?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListTravelPackageCalendarListVo {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: TravelPackageCalendarListVo[];
  success?: boolean;
}

/** 低空套餐 获取套餐库存日历 */
export interface TravelPackageCalendarListVo {
  /**
   * 产品ID
   * @format int64
   * @example 1
   */
  productId?: number;
  /**
   * 日期
   * @example "2020-07-08"
   */
  flightDate?: string;
  /**
   * 库存
   * @format int32
   * @example 49
   */
  leftInventory?: number;
  /**
   * 架次名称
   * @example "名称"
   */
  sortiesName?: string;
  /**
   * 尾款
   * @example 2500
   */
  endPaymentPrice?: number;
  /**
   * 定金
   * @example 500
   */
  advancePaymentPrice?: number;
  /**
   * 总价
   * @example 500
   */
  totalPaymentPrice?: number;
}

/** 低空订单出票或取消参数 */
export interface TravelIssueOrCancelDTO {
  /**
   * 订单号
   * @example *********
   */
  orderNo: string;
  /**
   * 创建人
   * @example "vx*********123213"
   */
  createBy: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultBoolean {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: boolean;
  success?: boolean;
}

/** 低空占座 */
export interface TravelBookingDTO {
  /**
   * 订单总价
   * @example 100
   */
  totalPaymentPrice: number;
  /**
   * 订单预付款总价
   * @example 100
   */
  advancePaymentPrice: number;
  /**
   * 订单尾款总价
   * @example 0
   */
  finalPaymentPrice: number;
  /**
   * 订单备注
   * @example "备注"
   */
  remark?: string;
  /**
   * 订单联系人
   * @example "联系人"
   */
  contactName: string;
  /**
   * 订单联系电话
   * @example "联系电话"
   */
  contactPhone: string;
  /**
   * 产品编号
   * @example "PN20200410*********"
   */
  productNo: string;
  /**
   * 订单类型1高空跳伞、2滑翔伞、3热气球、4低空旅游
   * @format int32
   * @example 1
   */
  orderType: number;
  /** 旅客信息 */
  salePassengersParam: TravelBookingPassengerDTO[];
  /**
   * 产品日期
   * @example "2021-05-01"
   */
  bookDate: string;
  /**
   * 创建人 订单创建者标识
   * @example "vx*********123213"
   */
  createBy: string;
  /**
   * 创建人姓名
   * @example "vx*********123213"
   */
  createName?: string;
  /**
   * 代理人
   * @example "跳伞之家"
   */
  agentName?: string;
}

/** 低空占座订单下的旅客信息 */
export interface TravelBookingPassengerDTO {
  /**
   * 订单总价
   * @example 100
   */
  totalPaymentPrice: number;
  /**
   * 订单预付款总价
   * @example 100
   */
  advancePaymentPrice: number;
  /**
   * 订单尾款总价
   * @example 0
   */
  finalPaymentPrice: number;
  /**
   * 旅客姓名
   * @example "张三"
   */
  name: string;
  /**
   * 旅客证件号
   * @example "S*********"
   */
  idNo: string;
  /**
   * 证件类型 Identity 身份证 passport护照 birthday 出生年月 other其他
   * @example "Identity"
   */
  idType: string;
  /**
   * 联系电话
   * @example "S*********01"
   */
  phone?: string;
  /**
   * 出生日期
   * @example "2000-01-02"
   */
  birthday: string;
  /**
   * 套餐编号
   * @example "S20200410*********"
   */
  packageNo: string;
  /**
   * 架次唯一编号
   * @example "S20200410*********"
   */
  sortiesNo: string;
  /**
   * 来自哪里
   * @example "重庆"
   */
  fromWhere?: string;
  /**
   * 几点到基地
   * @example "10:10"
   */
  arriveTime?: string;
  /**
   * 体重
   * @example 100
   */
  weight?: string;
  /**
   * 备注
   * @example "备注"
   */
  remark?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultTravelOrderVo {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: TravelOrderVo;
  success?: boolean;
}

/** 低空订单信息 */
export interface TravelOrderVo {
  /**
   * 订单号
   * @example "张三"
   */
  orderNo?: string;
  /**
   * 订单状态 1占座，2出票,3取消
   * @format int32
   * @example 1
   */
  status?: number;
  /**
   * 订单总价
   * @example 100
   */
  totalPaymentPrice?: number;
  /**
   * 订单预付款金额
   * @example 100
   */
  advancePaymentPrice?: number;
  /**
   * 订单尾款金额
   * @example 0
   */
  finalPaymentPrice?: number;
  /**
   * 订单预付款状态 0未付 1已付
   * @format int32
   * @example 1
   */
  advancePaymentStatus?: number;
  /**
   * 订单尾款状态 0未付 1已付
   * @format int32
   * @example 1
   */
  finalPaymentStatus?: number;
  /**
   * 订单预付款支付时间
   * @format date-time
   * @example "2020-04-11 14:08:00"
   */
  advancePaymentPayTime?: string;
  /**
   * 订单尾款支付时间
   * @format date-time
   * @example "2020-04-11 14:08:00"
   */
  finalPaymentPayTime?: string;
  /**
   * 订单下的旅客数量
   * @format int32
   * @example 1
   */
  passengerCount?: number;
  /**
   * 订单备注
   * @example "备注"
   */
  remark?: string;
  /**
   * 订单联系人
   * @example "张三"
   */
  contactName?: string;
  /**
   * 订单联系电话
   * @example 13888888888
   */
  contactPhone?: string;
  /**
   * 创建者
   * @example "xx*********12312"
   */
  createBy?: string;
  /**
   * 订单创建时间
   * @format date-time
   * @example "2020-04-11 14:08:00"
   */
  createTime?: string;
  /**
   * 出票时间
   * @format date-time
   * @example "2020-04-11 14:08:00"
   */
  issueTime?: string;
  /**
   * 航司名称
   * @example "航空公司"
   */
  carrierName?: string;
  /**
   * 代理人（渠道）名称
   * @example "张三"
   */
  agentName?: string;
  /**
   * 航司代码
   * @example "CA"
   */
  carrierCode?: string;
  /**
   * 代理人（渠道）代码
   * @example "CA"
   */
  agentCode?: string;
  /**
   * 产品类型1高空跳伞、2滑翔伞、3热气球、4低空旅游
   * @format int32
   * @example 1
   */
  orderType?: number;
  /**
   * 基地代码代码
   * @example "CKG"
   */
  areaCode?: string;
  /** 旅客信息 */
  salePassengersResult?: TravelPassengerVo[];
}

/** 低空旅客下的产品信息 */
export interface TravelPassengerProductVo {
  /**
   * 产品编号
   * @example 1
   */
  productNo?: string;
  /**
   * 产品日期
   * @example "2019-01-01"
   */
  productDate?: string;
  /**
   * 套餐编号
   * @example 1
   */
  packageNo?: string;
  /**
   * 产品名称
   * @example "产品名称"
   */
  productName?: string;
  /**
   * 产品类型1 跳伞 2 滑翔机 3热气球 4 低空游览
   * @format int32
   * @example 1
   */
  productType?: number;
  /**
   * 产品说明
   * @example "产品说明"
   */
  productDescription?: string;
  /**
   * 套餐名称
   * @example "套餐名称"
   */
  packageName?: string;
  /**
   * 套餐说明
   * @example "套餐说明"
   */
  packageDescription?: string;
  /**
   * 套餐总价
   * @example 300
   */
  packageTotalPaymentPrice?: number;
  /**
   * 套餐预付款
   * @example 300
   */
  packageAdvancePaymentPrice?: number;
  /**
   * 套餐尾款
   * @example 0
   */
  packageFinalPaymentPrice?: number;
  /**
   * 套餐摄像类型名称
   * @example "套餐摄像类型名称"
   */
  packageCameraTypeName?: string;
  /**
   * 架次起飞时间
   * @example "12:00"
   */
  sortiesDepTime?: string;
  /**
   * 架次到达时间
   * @example "13:00"
   */
  sortiesArrTime?: string;
  /**
   * 到达基地名称
   * @example "到达基地名称"
   */
  arrCity?: string;
  /**
   * 到达基地代码
   * @example "PEK"
   */
  arrCode?: string;
  /**
   * 起飞基地名称
   * @example "起飞基地名称"
   */
  depCity?: string;
  /**
   * 起飞基地代码
   * @example "CKG"
   */
  depCode?: string;
  /**
   * 高度
   * @example 3000
   */
  skyHeight?: string;
  /**
   * 架次编号
   * @example *********123
   */
  sortieNo?: string;
}

/** 低空旅客信息 */
export interface TravelPassengerVo {
  /**
   * 订单号
   * @example 1215165165156156
   */
  orderNo?: string;
  /**
   * 票号
   * @example 1215165165156156
   */
  ticketNo?: string;
  /**
   * 票状态 0占座  1取消  2出票  4退票 5已使用 6改签
   * @format int32
   * @example 1
   */
  ticketStatus?: number;
  /**
   * 旅客姓名
   * @example "张三"
   */
  passengerName?: string;
  /**
   * 旅客证件号
   * @example 1215165165156156
   */
  idNo?: string;
  /**
   * Identity 身份证 passport护照 birthday 出生年月 other其他
   * @example 1
   */
  idType?: string;
  /**
   * 联系电话
   * @example 1215165165156156
   */
  phone?: string;
  /**
   * 出生日期 2000-01-02
   * @example "2000-01-02"
   */
  birthday?: string;
  /**
   * 航班日期
   * @example "2000-01-02"
   */
  flightDate?: string;
  /**
   * 航班号
   * @example "HL1231"
   */
  flightNo?: string;
  /**
   * 备注
   * @example "备注"
   */
  remark?: string;
  /**
   * 航司名称
   * @example "南方航空"
   */
  carrierName?: string;
  /**
   * 航司代码
   * @example "CA"
   */
  carrierCode?: string;
  /**
   * 基地代码
   * @example "PEK"
   */
  areaCode?: string;
  /**
   * 代理人（渠道）代码
   * @example "CA"
   */
  agentCode?: string;
  /**
   * 代理结算价
   * @example 3000
   */
  agentSettlementPrice?: number;
  /**
   * 是否使用 1已使用 0未使用
   * @format int32
   * @example 1
   */
  isUsed?: number;
  /**
   * 是否联系 0默认 未联系； 1已联系 2 待定
   * @format int32
   * @example 0
   */
  isConfirm?: number;
  /**
   * 订单预付款状态 0未付 1已付
   * @format int32
   * @example 1
   */
  advancePaymentStatus?: number;
  /**
   * 订单尾款状态 0未付 1已付
   * @format int32
   * @example 1
   */
  finalPaymentStatus?: number;
  /**
   * 订单预付款支付时间
   * @format date-time
   * @example "2000-01-02"
   */
  advancePaymentPayTime?: string;
  /**
   * 订单预尾款支付时间
   * @format date-time
   * @example "2000-01-02"
   */
  finalPaymentPayTime?: string;
  /**
   * 退票客规
   * @example "0-24-100"
   */
  refundRule?: string;
  /**
   * 改签客规
   * @example "0-24-100"
   */
  rebookRule?: string;
  /**
   * 创建人
   * @example "创建人"
   */
  createBy?: string;
  /**
   * 创建者姓名
   * @example "张三"
   */
  createName?: string;
  /** 产品套餐架次信息 */
  passengerProduct?: TravelPassengerProductVo;
  /**
   * 代理人（渠道）名称
   * @example "代理人（渠道）名称"
   */
  agentName?: string;
  /**
   * 公司实际收入 尾款-代理结算价
   * @example 100
   */
  companyRealIncome?: number;
  /**
   * 来自哪里
   * @example "重庆"
   */
  fromWhere?: string;
  /**
   * 几点到基地
   * @example "10:10"
   */
  arriveTime?: string;
  /**
   * 体重
   * @example 100
   */
  weight?: string;
  /**
   * 添加渠道 1小程序 2后台直接录入
   * @format int32
   * @example 1
   */
  channel?: number;
  /**
   * 预约时间
   * @example "2022-10-10 15:20"
   */
  appointmentTime?: string;
  /**
   * 剩余支付
   * @example 0
   */
  remainingPayment?: number;
  /**
   * 跳伞协议
   * @example "地址"
   */
  agreement?: string;
  /**
   * 是否通过实名认证
   * @format int32
   * @example 1
   */
  isRealAuth?: number;
  /**
   * 人脸识别图片
   * @example "地址"
   */
  realFaceImage?: string;
  /**
   * 核验码
   * @example "KY123456"
   */
  verificationCode?: string;
}

/** 短信验证码请求参数 */
export interface SmsCodeRequestDTO {
  /**
   * 手机号
   * @minLength 1
   * @pattern ^1[3-9]\d{9}$
   * @example 13800138000
   */
  phoneNumber: string;
  /**
   * 图形验证码
   * @minLength 1
   * @example 1234
   */
  captchaCode: string;
  /**
   * 验证码标识，由获取图形验证码接口返回
   * @minLength 1
   * @example 165246811200012350
   */
  captchaKey: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultString {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: string;
  success?: boolean;
}

/** 刷新token */
export interface RefreshDTO {
  /**
   * 刷新token
   * @example "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIi"
   */
  refreshToken?: string;
  /**
   * 用户ID
   * @example 1
   */
  uid?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultLoginVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: LoginVO;
  success?: boolean;
}

/** 登录 */
export interface LoginVO {
  /**
   * 用户类型，1、散客 2、企业
   * @format int32
   */
  type?: number;
  /** 当前用户认证Token */
  accessToken?: string;
  /** 用户刷新认证Token */
  refreshToken?: string;
  /** 用户ID */
  uid?: string;
  /** 当前用户认证Token过期时间 */
  accessTokenTimeOut?: string;
  /** 用户刷新认证Token过期时间 */
  refreshTokenTimeOut?: string;
}

/** 用户登陆 */
export interface LoginDTO {
  /**
   * 11位国内电话号码
   * @example 11111111111
   */
  phoneNumber?: string;
  /**
   * 短信验证码
   * @example 268102
   */
  verificationCode?: string;
  /**
   * 登录类型 1、手机号登录,2、微信快捷登录,为1时必须传输phoneNumber、verificationCode,为2时必须传输wechatCode,不传默认1手机号登录,3、openId登录
   * @format int32
   * @example 1
   */
  loginType: number;
  /** 微信动态令牌-手机号 ，小程序<button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber"></button> */
  wechatCode?: string;
  /** wechatOpenid */
  wechatOpenid?: string;
  /** 获取openId的code */
  code?: string;
  /**
   * APP类型   1 微信小程序,2 安卓, 3 IOS
   * @format int32
   */
  appType?: number;
}

/** 退出登录 */
export interface LoginOutDTO {
  /**
   * 用户ID
   * @example 1
   */
  uid?: string;
  /** 当前用户刷新Token */
  refreshToken?: string;
}

/** 图形验证码响应 */
export interface CaptchaImageDTO {
  /** 验证码Base64编码图片 */
  captchaBase64?: string;
  /** 验证码标识，发送短信时需要使用该值进行验证 */
  captchaKey?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultCaptchaImageDTO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: CaptchaImageDTO;
  success?: boolean;
}

/** 更新常用乘机人信息 */
export interface UpdateCommonPassengerDTO {
  /**
   * 乘机人id
   * @format int64
   */
  id: number;
  /** 常用旅客姓名,身份证、户口簿填写 */
  name?: string;
  /** 姓,护照、回乡证、台胞证、出生证明、其他、外国人永居证填写 */
  firstName?: string;
  /** 名,护照、回乡证、台胞证、出生证明、其他、外国人永居证填写 */
  nextName?: string;
  /**
   * 常用旅客手机号
   * @minLength 1
   * @pattern ^1[3-9]\d{9}$
   */
  mobile: string;
  /** 常用旅客性别 */
  sex: string;
  /** 常用旅客证件类型 */
  certType: string;
  /**
   * 常用旅客证件号码
   * @minLength 1
   */
  certNo: string;
  /** 常用旅客证件有效期 */
  certValidity: string;
  /** 证件签发国 */
  issuerCountry: string;
  /** 国籍 */
  nationality?: string;
  /** 出生日期 */
  birthday?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListCommonPassengerListVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: CommonPassengerListVO[];
  success?: boolean;
}

/** 常用旅客列表 */
export interface CommonPassengerListVO {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 常用旅客姓名, 身份证、户口簿填写 */
  name?: string;
  /** 姓 , 护照、回乡证、台胞证、出生证明、其他、外国人永居证填写 */
  firstName?: string;
  /** 名 , 护照、回乡证、台胞证、出生证明、其他、外国人永居证填写 */
  nextName?: string;
  /** 常用旅客手机号 */
  mobile: string;
  /** 常用旅客性别 */
  sex: string;
  /**
   * 常用旅客证件类型
   * @format int32
   */
  certType: number;
  /** 常用旅客证件号码 */
  certNo: string;
  /** 常用旅客证件有效期 */
  certValidity: string;
  /** 证件签发国 */
  issuerCountry: string;
  /** 国籍 */
  nationality?: string;
  /** 出生日期 */
  birthday?: string;
}

/** 删除常用乘机人信息 */
export interface CommonPassengerDeleteDTO {
  /** 乘机人id */
  id: string;
}

/** 常用旅客信息 */
export interface AddCommonPassengerDTO {
  /**
   * 常用旅客姓名 ，身份证、户口簿需要填写
   * @minLength 0
   * @maxLength 20
   */
  name?: string;
  /**
   * 姓 ，护照、回乡证、台胞证、出生证明、其他、外国人永居证需要填写
   * @minLength 0
   * @maxLength 20
   */
  firstName?: string;
  /** 名 ，护照、回乡证、台胞证、出生证明、其他、外国人永居证需要填写 */
  nextName?: string;
  /**
   * 常用旅客手机号
   * @minLength 1
   * @pattern ^1[3-9]\d{9}$
   */
  mobile: string;
  /** 常用旅客性别 */
  sex: string;
  /** 常用旅客证件类型，询问开发 */
  certType?: string;
  /**
   * 常用旅客证件号码
   * @minLength 1
   */
  certNo: string;
  /** 常用旅客证件有效期 */
  certValidity?: string;
  /** 证件签发国 */
  issuerCountry?: string;
  /** 国籍 */
  nationality?: string;
  /** 出生日期 */
  birthday?: string;
}

/** 订单列表查询参数 */
export interface CommonOrderListDTO {
  /**
   * 订单类型 null所有 0短途运输 1低空游览 2包机订单
   * @format int32
   */
  type?: number;
  /**
   * 当前页,从1开始
   * @format int32
   * @min 1
   */
  page: number;
  /**
   * 每页条数，最小5,最大20
   * @format int32
   * @min 5
   * @max 20
   */
  pageSize: number;
}

/** 通用 API 返回结果 */
export interface CommonApiResultCommonOrderListVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: CommonOrderListVO;
  success?: boolean;
}

/** 订单信息 */
export interface CommonOrderBO {
  /** 订单号 */
  orderNo?: string;
  /**
   * 订单类型1短途运输、2低空游览、3包机订单
   * @format int32
   */
  type?: number;
  /**
   * 订单状态,0 占座未支付 、1 出票中 、2 已出票 、3 有退改 -1 已取消
   * @format int32
   */
  status?: number;
  /**
   * 价格，元
   * @format double
   */
  price?: number;
  /** 支付单号 */
  payNo?: string;
  /**
   * 支付状态,0未支付、1支付成功
   * @format int32
   */
  payStatus?: number;
  /** 订单创建时间 yyyy-MM-dd HH:mm:ss */
  createTime?: string;
  /** 支付时间 yyyy-MM-dd HH:mm:ss */
  payTime?: string;
  /** 出票时间 yyyy-MM-dd HH:mm:ss */
  issueTime?: string;
  /** 短途运输订单信息 */
  shortRouteOrderFlights?: ShortRouteOrderListFlightBO[];
}

/** 订单列表 */
export interface CommonOrderListVO {
  /** 订单列表 */
  orders?: CommonOrderBO[];
  /**
   * 总条数
   * @format int64
   */
  total?: number;
  /**
   * 当前页
   * @format int32
   */
  page?: number;
  /**
   * 每页条数
   * @format int32
   */
  pageSize?: number;
  /**
   * 总页数
   * @format int32
   */
  totalPage?: number;
}

/** 订单信息 */
export interface ShortRouteOrderListFlightBO {
  /** 出发地三字码 */
  depCode?: string;
  /** 到达地三字码 */
  arrCode?: string;
  /** 中转地三字码 */
  stopoverCode?: string;
  /** 出发地 */
  depCity?: string;
  /** 到达地 */
  arrCity?: string;
  /** 中转地 */
  stopoverCity?: string;
  /** 航班号 */
  flightNo?: string;
  /** 出发日期 yyyy-MM-dd */
  flightDate?: string;
  /** 出发时间 HH:mm */
  planDepTime?: string;
  /** 到达时间 HH:mm */
  planArrTime?: string;
  /** 航司logo图片地址 */
  carrierLogoUrl?: string;
  /**
   * 航班类型 0单程 1经停 2中转
   * @format int32
   */
  flightType?: number;
  /** 航司代码 */
  carrierCode?: string;
  /** 航司名称 */
  carrierName?: string;
  /** 舱位代码 */
  cabinCode?: string;
  /** 舱位名称 */
  cabinName?: string;
  /** 机型 */
  aircraft?: string;
  /** 机型尾号 */
  aircrafttailNo?: string;
  /** 飞行时间 1h20m */
  flytime?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListNationalityVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: NationalityVO[];
  success?: boolean;
}

/** 国籍返回 */
export interface NationalityVO {
  /** 国籍代码 */
  code?: string;
  /** 国籍名称 */
  name?: string;
}

export interface LocationDTO {
  /** 经度 */
  longitude?: string;
  /** 纬度 */
  latitude?: string;
}

export interface CityInfoVO {
  /**
   * 城市ID
   * @format int64
   */
  cityId?: number;
  /** 城市名称 */
  cityName?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultCityInfoVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: CityInfoVO;
  success?: boolean;
}

/** 首页请求参数 */
export interface BannerHotDTO {
  /**
   * 当前定位城市
   * @example "成都"
   */
  currentCity?: string;
  /**
   * 当前定位城市ID
   * @example 1
   */
  currentCityId?: string;
}

/** 首页返回数据内容 */
export interface BannerHotVO {
  /** 轮播图列表 */
  banners?: BannerVO[];
  /** 热门推荐产品列表 */
  hotProducts?: HotProductVO[];
}

/** 轮播图 */
export interface BannerVO {
  /**
   * 轮播图图片 URL
   * @example "https://example.com/banner1.jpg"
   */
  imageUrl?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultBannerHotVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: BannerHotVO;
  success?: boolean;
}

/** 热门推荐产品 */
export interface HotProductVO {
  /**
   * 产品名称
   * @example "智能手表"
   */
  name?: string;
  /**
   * 产品标签列表
   * @example ["科技","健康"]
   */
  tags?: string[];
  /**
   * 产品图片 URL
   * @example "https://example.com/product1.jpg"
   */
  imageUrl?: string;
  /**
   * 产品价格，单位：元
   * @format double
   * @example 299.99
   */
  price?: number;
  /**
   * 产品类型 1、短途运输 2、空中游览  3、低空航线
   * @format int32
   */
  type?: number;
  /**
   * 产品编号
   * @example 11345678670
   */
  productNo?: string;
  /**
   * 航班号
   * @example "HL2025"
   */
  flightNo?: string;
  /**
   * 航班日期
   * @example "2025-05-24"
   */
  flightDate?: string;
  /**
   * 出发城市
   * @example "上海"
   */
  depCity?: string;
  /**
   * 到达城市
   * @example "北京"
   */
  arrCity?: string;
}

/** 支付参数 */
export interface CommonOrderDTO {
  /** 订单号 */
  orderNo?: string;
  /**
   * 价格
   * @format double
   */
  price?: number;
}

/** 通用 API 返回结果 */
export interface CommonApiResultCommonOrderPayVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: CommonOrderPayVO;
  success?: boolean;
}

/** 订单支付 */
export interface CommonOrderPayVO {
  /** 微信返回【随机字符串】不长于32位。该值建议使用随机数算法生成。 */
  nonceStr?: string;
  /** 微信返回【统一下单接口返回的 wxPackage】 */
  wxPackage?: string;
  /** 微信返回【签名算法】 */
  signType?: string;
  /** 微信返回【签名】 */
  paySign?: string;
  /** 微信返回【时间戳】 */
  timeStamp?: string;
  /** 订单价格 */
  orderPrice?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListShortRouteAppTipVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteAppTipVO[];
  success?: boolean;
}

/** 短途运输APP提示VO */
export interface ShortRouteAppTipVO {
  /** 提示信息 */
  tip?: string;
  /** 提示信息URL,富文本 */
  url?: string;
}

/** 退款请求参数 */
export interface ShortRouteRefundDTO {
  /** 订单号 */
  orderNo?: string;
  /** 客票号 */
  tickets?: TicketNoBO[];
  /**
   * 退票类型,0自愿 1非自愿
   * @format int32
   */
  type?: number;
  /**
   * 申请退票价格,自愿必填，非自愿不填
   * @format double
   */
  applyRefundPrice?: number;
  /** 申请原因,不超过30字符 */
  applyReason?: string;
}

/** 票信息 */
export interface TicketNoBO {
  /** 票号 */
  ticketNo?: string;
}

/** 短程退票价格查询 */
export interface ShortRouteRefundPriceDTO {
  /** 订单号 */
  orderNo?: string;
  /** 票号集合,不传查所有 */
  tickets?: TicketNoBO[];
}

/** 通用 API 返回结果 */
export interface CommonApiResultShortRouteRefundPriceVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteRefundPriceVO;
  success?: boolean;
}

/** 退票明细 */
export interface RefundPricePassengerBO {
  /**
   * 实退
   * @format double
   */
  refundActual?: number;
  /**
   * 基建退费
   * @format double
   */
  refundAirportTax?: number;
  /**
   * 旅客退票手续费
   * @format double
   */
  refundFee?: number;
  /**
   * 燃油退费
   * @format double
   */
  refundFuelTax?: number;
  /**
   * 应退
   * @format double
   */
  refundRequired?: number;
  /** 票号 */
  ticketNo?: string;
}

/** 短程退票价格查询返回值 */
export interface ShortRouteRefundPriceVO {
  /**
   * 退票百分比
   * @format double
   */
  refundPercent?: number;
  /**
   * 退单总额(实退)
   * @format double
   */
  refundActualTotal?: number;
  /**
   * 应退总额
   * @format double
   */
  refundRequiredTotal?: number;
  /**
   * 退票手续总额
   * @format double
   */
  refundFeeTotal?: number;
  /**
   * 基建退票总额
   * @format double
   */
  refundTaxTotal?: number;
  /**
   * 燃油退票总额
   * @format double
   */
  refundFuelTotal?: number;
  /**
   * 税费退票总额
   * @format double
   */
  refundTaxAndFuelTotal?: number;
  /** 退票明细 */
  passengers?: RefundPricePassengerBO[];
}

/** 通用 API 返回结果 */
export interface CommonApiResultListShortRouteRecommendVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteRecommendVO[];
  success?: boolean;
}

/** 短途航线推荐返回数据内容 */
export interface ShortRouteRecommendVO {
  /** 出发城市 */
  depCity?: string;
  /** 出发机场编码 */
  depCode?: string;
  /** 到达城市 */
  arrCity?: string;
  /** 到达机场编码 */
  arrCode?: string;
  /**
   * 价格
   * @format double
   */
  price?: number;
  /** 图片URL */
  imageUrl?: string;
}

/** 行李信息 */
export interface Baggage {
  /** 限制大小CM  20*30*40 */
  limitSize?: string;
  /**
   * 限制携带数 件  1件
   * @format int64
   */
  limitNum?: number;
  /**
   * n件行李限制总重量 KG  20
   * @format int64
   */
  limitWeight?: number;
  /**
   * 单件行李限制重量 kg
   * @format float
   */
  simpleLimitWeight?: number;
}

/** 舱位行李信息 */
export interface CabinBaggage {
  /** 手提行李 */
  handBaggage?: Baggage;
  /** 托运行李 */
  regBaggage?: Baggage;
  /** 是否有免费手提行李 */
  freeHandBaggage?: boolean;
  /** 是否有免费托运行李 */
  freeRegBaggage?: boolean;
}

/** 航班舱位信息 */
export interface FlightCabinBO {
  /** 舱位代码 */
  code?: string;
  /**
   * 折扣后的价格
   * @format double
   */
  price?: number;
  /** 舱位类型名称 */
  cabinType?: string;
  /**
   * 舱位成人原价
   * @format double
   */
  originalPrice?: number;
  /**
   * 舱位儿童原价
   * @format double
   */
  originalChildPrice?: number;
  /**
   * 舱位婴儿原价
   * @format double
   */
  originalInfantPrice?: number;
  /**
   * 优惠百分比
   * @format double
   */
  discountPer?: number;
  /**
   * 婴儿价格
   * @format double
   */
  infantPrice?: number;
  /**
   * 儿童价格
   * @format double
   */
  childPrice?: number;
  /** 实际舱位代码 */
  actCabinCode?: string;
  /**
   * 剩余座位数
   * @format int64
   */
  seat?: number;
  /**
   * 第一次加入会员价格
   * @format double
   */
  vipFirstJoinPrice?: number;
  /**
   * 婴儿剩余座位
   * @format int64
   */
  infantSeat?: number;
  /** 婴儿是否占座 */
  infantUseSeat?: boolean;
  /** 退票规则   100-0-100 */
  refundRule?: string;
  /** 退票规则，先判断客票状态，客票状态为出票的才有值 */
  refundRuleCns?: ShortRouteOrderDetailRefundRuleBO[];
  /** 改签规则，先判断客票状态，客票状态为出票的才有值,为空表示不支持退票 */
  rebookRuleCns?: ShortRouteOrderDetailRebookRuleBO[];
  /**
   * 第一程成人舱位价格
   * @format double
   */
  firstCabinPrice?: number;
  /**
   * 第二程成人舱位价格
   * @format double
   */
  nextCabinPrice?: number;
  /**
   * 第一程儿童舱位价格
   * @format double
   */
  firstCabinChildPrice?: number;
  /**
   * 第二程儿童舱位价格
   * @format double
   */
  nextCabinChildPrice?: number;
  /**
   * 第一程婴儿舱位价格
   * @format double
   */
  firstCabinInfantPrice?: number;
  /**
   * 第二程婴儿舱位价格
   * @format double
   */
  nextCabinInfantPrice?: number;
  /** 第一程舱位代码 */
  firstCabinCode?: string;
  /** 第二程舱位代码 */
  nextCabinCode?: string;
  /** 改签规则 100-0-100无法改签 */
  rebookRule?: string;
  /** 是否限制年龄 */
  limitAge?: boolean;
  /**
   * 最小限制年龄
   * @format int32
   */
  limitAgeMin?: number;
  /**
   * 最大限制年龄
   * @format int32
   */
  limitAgeMax?: number;
  /** 行李规定 */
  baggage?: CabinBaggage;
}

/** 旅客退票规则 */
export interface ShortRouteOrderDetailRebookRuleBO {
  /**
   * 改签时间
   * @example "2025-06-10 13:14(前)"
   */
  rebookTime?: string;
  /**
   * 改日期手续费
   * @format double
   * @example 100
   */
  rebookDateFee?: number;
  /** 时间是否已过期 */
  isExpired?: boolean;
  /** 是否是当前时间规则 */
  isCurrentTimeRule?: boolean;
}

/** 旅客退票规则 */
export interface ShortRouteOrderDetailRefundRuleBO {
  /**
   * 退票时间
   * @example "2025-06-10 13:14(前)"
   */
  refundTime?: string;
  /**
   * 退票手续费
   * @format double
   * @example 100
   */
  refundFee?: number;
  /**
   * 预计退还
   * @format double
   * @example "100,00"
   */
  refundAmount?: number;
  /** 时间是否已过期 */
  isExpired?: boolean;
  /** 是否是当前时间规则 */
  isCurrentTimeRule?: boolean;
}

/** 价格查询请求参数,调用前判断航班的infantUseSeat是否支持婴儿购买，不支持不允许勾选婴儿。除了出生日期，其他参数都是航班查询传输的信息 */
export interface ShortRoutePriceDTO {
  /**
   * 出生日期集合，在航班起飞前2岁以下是婴儿，2-12是儿童，12岁以上成人
   * @example ["2025-06-04","2025-06-04"]
   */
  passengers?: ShortRoutePricePassengerBO[];
  /** 价格计算航班信息集合 */
  flights?: ShortRoutePriceFlightBO[];
}

/** 价格计算航班信息 */
export interface ShortRoutePriceFlightBO {
  /** 计划起飞时间 */
  planDepartTime?: string;
  /** 计划到达时间 */
  planArriveTime?: string;
  /** 舱位信息 */
  cabin?: FlightCabinBO;
  /** 航班日期 */
  flightDate?: string;
  /** 航班号 */
  flightNo?: string;
  /** 到达城市三字码 */
  arriveAirportCode?: string;
  /** 起始机场三字码 */
  departAirportCode?: string;
  /**
   * 机建
   * @format int64
   */
  tax?: number;
  /**
   * 燃油
   * @format int64
   */
  fuel?: number;
  /**
   * 税费（成人）
   * @format int64
   */
  taxAndFees?: number;
  /**
   * 机建(儿童)
   * @format int64
   */
  childTax?: number;
  /**
   * 燃油(儿童)
   * @format int64
   */
  childFuel?: number;
  /**
   * 税费（儿童）
   * @format int64
   */
  childTaxAndFees?: number;
  /**
   * 机建(婴儿)
   * @format int64
   */
  babyTax?: number;
  /**
   * 燃油(婴儿)
   * @format int64
   */
  babyFuel?: number;
  /**
   * 税费（婴儿）
   * @format int64
   */
  babyTaxAndFees?: number;
  /**
   * 最大售卖婴儿
   * @format int32
   */
  maxInfantMum?: number;
}

/** 价格查询旅客信息BO */
export interface ShortRoutePricePassengerBO {
  /** 乘机人姓名 , 证件类型为护照时没有该字段 */
  name?: string;
  /** 乘机人姓 , 证件类型为护照时必传 */
  firstName?: string;
  /** 乘机人名 , 证件类型为护照时必传 */
  nextName?: string;
  /** 出生日期 , yyyy-MM-dd除身份证、户口簿，其他证件都要传 */
  birthDay?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultShortRoutePriceVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRoutePriceVO;
  success?: boolean;
}

/** 价格查询返回航班详情 */
export interface ShortRoutePriceReturnFlightBO {
  /** 出发地三字码 */
  depCode?: string;
  /** 到达地三字码 */
  arrCode?: string;
  /** 航班日期 */
  flightDate?: string;
  /** 航班号 */
  flightNo?: string;
  /** 舱位代码 */
  cabinCode?: string;
  /**
   * 价格
   * @format double
   */
  price?: number;
  /**
   * 税费
   * @format double
   */
  taxAndFees?: number;
  /**
   * 支付价格
   * @format double
   */
  payPrice?: number;
  /** 价格查询返回旅客详情 */
  passengers?: ShortRoutePriceReturnPassengerBO[];
}

/** 价格查询返回旅客信息 */
export interface ShortRoutePriceReturnPassengerBO {
  /** 旅客姓名 */
  passengerName?: string;
  /**
   * 价格
   * @format double
   */
  price?: number;
  /**
   * 税费
   * @format double
   */
  taxAndFees?: number;
  /**
   * 支付价格
   * @format double
   */
  payPrice?: number;
}

/** 价格查询返回 */
export interface ShortRoutePriceVO {
  /** 航班信息 */
  flights?: ShortRoutePriceReturnFlightBO[];
  /**
   * 价格
   * @format double
   */
  price?: number;
  /**
   * 税费
   * @format double
   */
  taxAndFees?: number;
  /**
   * 支付价格
   * @format double
   */
  payPrice?: number;
}

/** 订单详情 */
export interface ShortRouteOrderDetailDTO {
  /** 订单号 */
  orderNo?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultShortRouteOrderDetailVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteOrderDetailVO;
  success?: boolean;
}

export interface ShortRouteOrderDetailBaggageBO {
  /** 航班日期 yyyy-MM-dd */
  flightDate?: string;
  /** 航班号 */
  flightNo?: string;
  /** 是否有免费手提行李 */
  freeHandBaggage?: boolean;
  /** 是否有免费托运行李 */
  freeRegBaggage?: boolean;
  /**
   * 免费手提行李数
   * @format int32
   */
  handLimitNum?: number;
  /**
   * 免费手提行李尺寸 长宽高cm
   * @example "20,50,70"
   */
  handLimitSize?: string;
  /**
   * 免费手提行李重量,kg
   * @format int32
   */
  handLimitWeight?: number;
  /**
   * 免费托运行李数
   * @format int32
   */
  regLimitNum?: number;
  /**
   * 免费托运行尺寸 长宽高cm
   * @example "20,50,70"
   */
  regLimitSize?: string;
  /**
   * 免费托运行李重量,kg
   * @format int32
   */
  regLimitWeight?: number;
}

/** 订单详情飞行计划详情 */
export interface ShortRouteOrderDetailFlightBO {
  /** 出发地三字码 */
  depCode?: string;
  /** 到达地三字码 */
  arrCode?: string;
  /** 中转地三字码 */
  stopoverCode?: string;
  /** 出发地 */
  depCity?: string;
  /** 到达地 */
  arrCity?: string;
  /** 中转地 */
  stopoverCity?: string;
  /** 航班号 */
  flightNo?: string;
  /** 出发日期 yyyy-MM-dd */
  flightDate?: string;
  /** 出发时间 HH:mm */
  planDepTime?: string;
  /** 到达时间 HH:mm */
  planArrTime?: string;
  /** 航司logo图片地址 */
  carrierLogoUrl?: string;
  /**
   * 航班类型 0单程 1经停 2中转
   * @format int32
   */
  flightType?: number;
  /** 航司代码 */
  carrierCode?: string;
  /** 航司名称 */
  carrierName?: string;
  /** 舱位代码 */
  cabinCode?: string;
  /** 舱位名称 */
  cabinName?: string;
  /** 机型 */
  aircraft?: string;
  /** 机型尾号 */
  aircrafttailNo?: string;
  /** 飞行时间 1h20m */
  flytime?: string;
  /** 旅客明细 */
  passengers?: ShortRouteOrderDetailPassengerBO[];
  /** 行李信息 */
  baggage?: ShortRouteOrderDetailBaggageBO;
}

/** 订单详情旅客详情 */
export interface ShortRouteOrderDetailPassengerBO {
  /** 姓名 */
  name?: string;
  /** 证件号码 */
  idCard?: string;
  /** 票号 */
  ticketNo?: string;
  /** 证件类型 */
  idType?: string;
  /**
   * 客票状态SUSPENDED	挂起，客票暂不能使用（对应订单中的占座未出票订单与申请退票的订单下的客票）
   * VOID	已作废（对应取消订单下的客票）
   * OPENFORUSE	客票有效（对应出票后的客票）
   * REFUNDED	已退票
   * CHECKIN	已值机
   * BOARDED	已登机
   * USED	已使用
   * EXCHANGED	已换开
   */
  status?: string;
  /** 客票状态中文 */
  statusCn?: string;
  /** 旅客类型 */
  type?: string;
  /** 联系电话 */
  phone?: string;
  /** 退票规则 */
  refundRule?: string;
  /**
   * 票价
   * @format double
   */
  price?: number;
  /**
   * 税费
   * @format int32
   */
  tax?: number;
  /** 退票规则，先判断客票状态，客票状态为出票的才有值,为空表示不支持退票 */
  refundRules?: ShortRouteOrderDetailRefundRuleBO[];
  /** 改签规则，先判断客票状态，客票状态为出票的才有值,为空表示不支持退票 */
  rebookRules?: ShortRouteOrderDetailRebookRuleBO[];
}

/** 订单详情退款信息 */
export interface ShortRouteOrderDetailRefundBO {
  /** 退款订单号 */
  refundNo?: string;
  /**
   * 退款金额
   * @format double
   */
  refundAmount?: number;
  /**
   * 申请退款金额
   * @format double
   */
  applyRefundAmount?: number;
  /** 申请时间 */
  applyTime?: string;
  /** 退款银行订单号 */
  bankRefundOrderNo?: string;
  /** 退款时间 */
  refundTime?: string;
  /**
   * 退款价格
   * @format double
   */
  bankRefundPrice?: number;
  /** 申请退款原因 */
  refundReason?: string;
  /**
   * 退款类型 0、null自愿 1非自愿
   * @format int32
   */
  refundType?: number;
  /** 审核时间 */
  auditTime?: string;
  /** 拒绝原因 */
  rejectReason?: string;
  /**
   * 退款状态 0申请中、 1审核成功、-1审核失败、 2已退款、-2退款失败
   * @format int32
   */
  refundStatus?: number;
  /** 退款状态 0申请中、 1审核成功、-1审核失败、 2已退款、-2退款失败 */
  refundStatusCn?: string;
  /** 航班日期 */
  flightDate?: string;
  /** 航班号 */
  flightNo?: string;
  /** 出发城市 */
  depCity?: string;
  /** 到达城市 */
  arrCity?: string;
  /** 旅客姓名 */
  passengerNames?: string[];
}

/** 订单详情 */
export interface ShortRouteOrderDetailVO {
  /** 订单号 */
  orderNo?: string;
  /**
   * 订单状态
   * @format int32
   */
  orderStatus?: number;
  /** 订单创建时间 */
  createTime?: string;
  /** 订单支付时间 */
  payTime?: string;
  /** 交易单号 */
  payNo?: string;
  /** 订单出票时间 */
  issueTime?: string;
  /** 联系人电话 */
  contactTele?: string;
  /** 联系人姓名 */
  contactName?: string;
  /** 订单详情航班信息 */
  flights?: ShortRouteOrderDetailFlightBO[];
  /** 订单退款信息 */
  refundOrderList?: ShortRouteOrderDetailRefundBO[];
}

/** 订单取消参数 */
export interface ShortRouteOrderCancelDTO {
  /** 订单号 */
  orderNo?: string;
}

/** 占座请求参数 */
export interface ShortRouteOccupyDTO {
  /** 航班信息 */
  flights?: ShortRouteOccupyFlightBO[];
  /** 旅客信息 */
  passengers?: ShortRouteOccupyPassengerBO[];
  /** 订单信息 */
  order?: ShortRouteOccupyOrderBO;
  /**
   * 航程信息,默认空,单程 ,0 单程 1往返 2多程
   * @format int32
   */
  segment?: number;
}

/** 航班信息 */
export interface ShortRouteOccupyFlightBO {
  /** 出发地三字码 */
  depCode?: string;
  /** 到达地三字码 */
  arrCode?: string;
  /** 航班日期 */
  flightDate?: string;
  /** 航班号 */
  flightNo?: string;
  /** 舱位代码 */
  cabinCode?: string;
  /**
   * 价格
   * @format double
   */
  price?: number;
}

/** 订单信息 */
export interface ShortRouteOccupyOrderBO {
  /** 手机号 */
  mobile?: string;
  /** 邮箱 */
  email?: string;
  /** 姓名 */
  userName?: string;
}

/** 生成订单接口，参数Passenger Object */
export interface ShortRouteOccupyPassengerBO {
  /** 乘机人姓名 , 证件类型为护照时没有该字段 */
  name?: string;
  /** 乘机人姓 , 证件类型为护照时必传 */
  firstName?: string;
  /** 乘机人名 , 证件类型为护照时必传 */
  nextName?: string;
  /** 证件类型  , 身份证、护照、户口薄等 */
  idType?: string;
  /** 证件号 , 证件对应证件号码 */
  idNo?: string;
  /** 旅客联系电话 , 旅客航变通知电话 */
  phone?: string;
  /** 出生日期 , yyyy-MM-dd除身份证、户口簿，其他证件都要传 */
  birthDay?: string;
  /** 国籍代码 , ISO6631-1二子码标准 除身份证、户口簿，其他证件都要传 */
  nationality?: string;
  /** 证件签发国 , ISO6631-1二子码标准 除身份证、户口簿，其他证件都要传 */
  idIssuingCountry?: string;
  /** 证件有效期 , yyyy-MM-dd 除身份证、户口簿，其他证件都要传 */
  idExpiryDate?: string;
  /**
   * 性别 , 1男 2女
   * @format int32
   */
  gender?: number;
}

/** 通用 API 返回结果 */
export interface CommonApiResultShortRouteOccupyVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteOccupyVO;
  success?: boolean;
}

/** 占座返回航班详情 */
export interface ShortRouteOccupyRFlightBO {
  /** 航班号 */
  flightNo?: string;
  /** 航班日期 yyyy-MM-dd */
  flightDate?: string;
  /** 出发时间 HH:mm */
  depTime?: string;
  /** 到达时间 HH:mm */
  arrTime?: string;
  /** 飞行时间 3h50m */
  flyTime?: string;
  /** 出发城市 */
  depCity?: string;
  /** 到达城市 */
  arrCity?: string;
  /** 出发机场 */
  depCode?: string;
  /** 到达机场 */
  arrCode?: string;
}

/** 旅客信息 */
export interface ShortRouteOccupyRPassengerBO {
  /** 乘机人姓名 , 证件类型为护照时没有该字段 */
  name?: string;
  /** 证件类型  , 身份证、护照、户口薄等 */
  idType?: string;
  /** 证件号 , 证件对应证件号码 */
  idNo?: string;
  /** 旅客联系电话 , 旅客航变通知电话 */
  phone?: string;
}

/** 支付详情 */
export interface ShortRouteOccupyRPaymentDetailsBO {
  /**
   * 价格
   * @format double
   */
  price?: number;
  /**
   * 税费
   * @format double
   */
  taxAndFees?: number;
  /**
   * 支付价格
   * @format double
   */
  payPrice?: number;
}

/** 占座返回 */
export interface ShortRouteOccupyVO {
  /** 订单号 */
  orderNo?: string;
  /**
   * 订单类型 1短途 2低空 3包机
   * @format int32
   */
  type?: number;
  /** 创建时间 yyyy-MM-dd HH:mm:ss */
  createTime?: string;
  /**
   * 订单价格 元
   * @format double
   */
  orderPrice?: number;
  /**
   * 订单状态 0未支付、-1订单取消、1出票中、2已出票、3有退改、4、已完成
   * @format int32
   */
  orderStatus?: number;
  /** 航班信息 */
  flights?: ShortRouteOccupyRFlightBO[];
  /** 支付详情 */
  paymentDetails?: ShortRouteOccupyRPaymentDetailsBO;
  /** 旅客信息 */
  passengers?: ShortRouteOccupyRPassengerBO[];
}

/** 短途航线查询参数 */
export interface ShortRouteAvDTO {
  /** 出发城市三字码 */
  depCode: string;
  /** 到达城市三字码 */
  arrCode: string;
  /** 出发日期 yyyy-MM-dd */
  flightDate: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListFlightPlanBO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: FlightPlanBO[];
  success?: boolean;
}

/** 航班附加权益 */
export interface FlightAppendServicePO {
  /** 航班日期 */
  flightDate?: string;
  /** 航班号 */
  flightNo?: string;
  /**
   * 权益中文描述(100字符以内)
   * @example "如遇航班任意一程发生延误或取消导致行程无法衔接的情况，旅客可拨打热线，航司将协助旅客免费办理退票或改签业务。"
   */
  descList: string;
  /**
   * 权益类型
   * @example "1、Hotel  酒店 2、Change 航变免费改 3、ACheckIn 一次值机 4、ASecCheck一次安检 5、RouteBaggage 行李直挂 6、Route 全程无忧"
   */
  type: string;
  /**
   * 权益类型中文 10个字符以内
   * @example "航班无忧"
   */
  info: string;
}

/** 航班基础信息 */
export interface FlightPlanBO {
  /** 航班日期 */
  flightDate?: string;
  /** 起始城市 */
  departCity?: string;
  /** 起始机场三字码 */
  departAirportCode?: string;
  /** 到达城市 */
  arriveCity?: string;
  /** 到达城市三字码 */
  arriveAirportCode?: string;
  /** 起始航站楼 */
  departTerminal?: string;
  /** 中转、经停地航站楼 */
  stopoverTerminal?: string;
  /** 到达航站楼 */
  arriveTerminal?: string;
  /**
   * 航程距离
   * @format int64
   */
  saillingDistance?: number;
  /**
   * 实际航程距离
   * @format int64
   */
  actualSaillingDistance?: number;
  /** 航班号 */
  flightNo?: string;
  /** 航班性质 */
  flightType?: string;
  /** 航班销售性质 */
  flightSaleType?: string;
  /** 航班销售状态 */
  flightSaleStatus?: string;
  /** 航班可飞状态 */
  flightFlyStatus?: string;
  /** 航班状态 */
  flightStatus?: string;
  /** 航班用途 */
  flightPurpose?: string;
  /**
   * 团购航班销售截止时间相对于起飞时间的提前小时数
   * @format int64
   */
  flightAheadTime?: number;
  /**
   * 团购航班最小可飞人数
   * @format int64
   */
  flightNumLimit?: number;
  /**
   * 航班价格（适用于包机航班）
   * @format double
   */
  flightPrice?: number;
  /** 计划起飞时间 */
  planDepartTime?: string;
  /** 计划到达时间 */
  planArriveTime?: string;
  /** 预计起飞时间 */
  predictDepartTime?: string;
  /** 预计到达时间 */
  predictArriveTime?: string;
  /** 实际起飞时间 */
  actualDepartTime?: string;
  /** 实际到达时间 */
  actualArriveTime?: string;
  /** 机尾号 */
  aircraftTailNo?: string;
  /**
   * 机建
   * @format int64
   */
  tax?: number;
  /**
   * 燃油
   * @format int64
   */
  fuel?: number;
  /** 所属通航公司 */
  carrier?: string;
  /**
   * 最大售卖婴儿
   * @format int32
   */
  maxInfantMum?: number;
  /** 婴儿是否占座 */
  infantUseSeat?: boolean;
  /**
   * 是否使用共享舱位设置
   * @format int32
   */
  isUsedShareCabinSetting?: number;
  /** 中转、经停地三字码 */
  stopoverCode?: string;
  /** 中转、经停城市 */
  stopoverCity?: string;
  /** 中转、经停时间 */
  stopoverTime?: string;
  /** 中转、经停地计划到达时间 */
  stopoverPlanArrTime?: string;
  /** 中转、经停地计划起飞时间 */
  stopoverPlanDepTime?: string;
  /** 产品说明,不超过500字符 */
  productManual?: string;
  /**
   * 提前销售天数
   * @format int32
   */
  advanceSaleDays?: number;
  /** 航班状态（第一段） */
  firstFlightStatus?: string;
  /** 航班状态（第二段） */
  secondFlightStatus?: string;
  /**
   * 公务机是否支持拼机
   * @format int32
   */
  businessSupportSimplePay?: number;
  /**
   * 公务机预计价格
   * @format double
   */
  businessPredictPrice?: number;
  /**
   * 航班销售类型Int值
   * @format int32
   */
  flightSaleTypeInt?: number;
  /** 航线详情信息 */
  flightSearchRouteMsgs?: FlightSearchRouteMsg[];
  /** 舱位信息 */
  flightPlanCabins?: FlightCabinBO[];
  /** 机型 */
  aircraft?: string;
  /** 共享航班号 */
  shareFlightNo?: string;
  /** 共享航司二字码 */
  shareCarrierTwoCode?: string;
  /** 共享航司名称 */
  shareCarrierName?: string;
  /**
   * 税费（成人）
   * @format int64
   */
  taxAndFees?: number;
  /**
   * 机建(儿童)
   * @format int64
   */
  childTax?: number;
  /**
   * 燃油(儿童)
   * @format int64
   */
  childFuel?: number;
  /**
   * 税费（儿童）
   * @format int64
   */
  childTaxAndFees?: number;
  /**
   * 机建(婴儿)
   * @format int64
   */
  babyTax?: number;
  /**
   * 燃油(婴儿)
   * @format int64
   */
  babyFuel?: number;
  /**
   * 税费（婴儿）
   * @format int64
   */
  babyTaxAndFees?: number;
  /** 权益列表 */
  flightAppendServices?: FlightAppendServicePO[];
  /** 计划到达时间是否是次日到达，只有干支通才需要有这个判断,通航都是当日达 */
  planArriveTomorrow?: boolean;
  /** 起飞时间日期，yyyy-MM-dd */
  planDepartTimeDate?: string;
  /** 到达时间日期  ,  yyyy-MM-dd */
  planArriveTimeDate?: string;
  /** 中转、经停地计划到达日期 , yyyy-MM-dd */
  stopoverPlanArrTimeDate?: string;
  /** 中转、经停地计划起飞日期 , yyyy-MM-dd */
  stopoverPlanDepTimeDate?: string;
  /** 航司名称 , 不超过20字符 */
  carrierName?: string;
  /** 航司logo地址 */
  carrierLogoUrl?: string;
}

export interface FlightSearchRouteMsg {
  /** @format int32 */
  routeNum?: number;
  depTime?: string;
  depCode?: string;
  depCityCnName?: string;
  arrCode?: string;
  arrCityCnName?: string;
  arrTime?: string;
  carrierCode?: string;
  carrierName?: string;
  flightNo?: string;
  aircraft?: string;
  aircraftTailNo?: string;
  /** @format int64 */
  tax?: number;
  /** @format int64 */
  fuel?: number;
  /** @format int64 */
  taxAndFees?: number;
  /** @format int64 */
  childTax?: number;
  /** @format int64 */
  childFuel?: number;
  /** @format int64 */
  childTaxAndFees?: number;
  /** @format int64 */
  babyTax?: number;
  /** @format int64 */
  babyFuel?: number;
  /** @format int64 */
  babyTaxAndFees?: number;
  aircraftSize?: string;
  meal?: string;
  /** @format double */
  combinedLimitPrice?: number;
  orderCombinedSys?: string;
  /**
   * 航距
   * @format int64
   */
  saillingDistance?: number;
  /**
   * 航班销售类型Int值
   * @format int32
   */
  flightSaleTypeInt?: number;
  /** 中转、经停地三字码 */
  stopoverCode?: string;
  /** 中转、经停城市 */
  stopoverCity?: string;
  /** 中转、经停时间 */
  stopoverTime?: string;
  /** 中转、经停地计划到达时间 */
  stopoverPlanArrTime?: string;
  /** 中转、经停地计划起飞时间 */
  stopoverPlanDepTime?: string;
  /** 中转、经停地航站楼 */
  stopoverTerminal?: string;
  /** 共享航班号 */
  shareFlightNo?: string;
  /** 共享航司二字码 */
  shareCarrierTwoCode?: string;
  /** 共享航司名称 */
  shareCarrierName?: string;
  /** 起飞时间日期,yyyy-MM-dd */
  depTimeDate?: string;
  /** 到达时间日期,yyyy-MM-dd */
  arrTimeDate?: string;
  /** 中转、经停地计划到达日期 */
  stopoverPlanArrTimeDate?: string;
  /** 中转、经停地计划起飞日期 */
  stopoverPlanDepTimeDate?: string;
}

/** 短途运输航线提示 */
export interface ShortRouteFlightTipDTO {
  /** 出发城市三字码 */
  depCode?: string;
  /** 到达城市三字码 */
  arrCode?: string;
  /** 航班号 */
  flightNo?: string;
  /** 承运公司三字码 */
  carrierCode?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListShortRouteFlightTipVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteFlightTipVO[];
  success?: boolean;
}

/** 短途运输航线提示 */
export interface ShortRouteFlightTipVO {
  /** 内容,《国内行李运输总条件》 */
  tip?: string;
  /** 提示链接 */
  urlList?: string[];
  /** 是否必读，如果是必读的话，需要弹窗url，旅客进行读取后才能进行客票购买 */
  isMustRead?: boolean;
}

/** 通用 API 返回结果 */
export interface CommonApiResultShortRouteCityVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteCityVO;
  success?: boolean;
}

/** 短途航线城市查询返回数据内容 */
export interface ShortRouteCityBO {
  /** 机场名称 */
  airportName?: string;
  /** 机场编码 */
  airportCode?: string;
}

/** 短途航线城市查询返回数据内容 */
export interface ShortRouteCityTitleLabelBO {
  /** 标题标签 */
  titleLabel?: string;
  /** 城市信息 */
  shortRouteCityList?: ShortRouteCityBO[];
}

/** 短途航线城市查询返回数据内容 */
export interface ShortRouteCityVO {
  /** 推荐出发地 */
  depAirportCode?: string;
  /** 出发地城市名称 */
  depAirportName?: string;
  /** 推荐目的地 */
  arrAirportCode?: string;
  /** 目的地城市名称 */
  arrAirportName?: string;
  /** 标签卡 */
  titleLabels?: ShortRouteCityTitleLabelBO[];
}

/** 改签 */
export interface ShortRouteChangeDTO {
  /** 订单号 */
  orderNo?: string;
  /**
   * 改签价格
   * @format double
   */
  price?: number;
  /** 改签日期 */
  rebookingDate?: string;
  /** 改签航班 */
  rebookingFlightNo?: string;
  /** 改签舱位 */
  cabinCode?: string;
  /**
   * 改签类型,0自愿 、1非自愿
   * @format int32
   */
  rebookingType?: number;
  /** 改签客票信息 */
  tickets?: TicketNoBO[];
}

/** 通用 API 返回结果 */
export interface CommonApiResultShortRouteChangeVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteChangeVO;
  success?: boolean;
}

/** 改签详情 */
export interface ShortRouteChangeDetailVO {
  /** 票号 */
  ticketNo?: string;
  /**
   * 价格
   * @format double
   */
  price?: number;
  /**
   * 改签价格
   * @format double
   */
  rebookingPrice?: number;
  /**
   * 改签日期价格
   * @format double
   */
  rebookingDatePrice?: number;
  /**
   * 改签舱位价格
   * @format double
   */
  rebookingCabinPrice?: number;
}

/** 改签 */
export interface ShortRouteChangeVO {
  /** 订单号 */
  orderNo?: string;
  /**
   * 改签状态 0占座 1出票 2订单取消 3出票中
   * @format int32
   */
  rebookingStatus?: number;
  /**
   * 改签支付状态 0未支付、1支付中
   * @format int32
   */
  rebookingPayStatus?: number;
  /** 改签日期 */
  rebookingDate?: string;
  /** 改签航班 */
  rebookingFlightNo?: string;
  /**
   * 改签价格
   * @format double
   */
  rebookingTotalPrice?: number;
  /**
   * 改签舱位价格
   * @format double
   */
  rebookingCabinTotalPrice?: number;
  /**
   * 改签日期价格
   * @format double
   */
  rebookingDateTotalPrice?: number;
  /** 改签退单号 */
  rebookingRefundNo?: string;
  /** 票号列表 */
  ticketNos?: ShortRouteChangeDetailVO[];
}

/** 改签查询参数 */
export interface ShortRouteChangeQueryDTO {
  /** 订单号 */
  orderNo?: string;
  /** 票号 */
  tickets?: TicketNoBO[];
  /** 改签日期,yyyy-MM-dd */
  rebookingDate?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultShortRouteChangeQueryVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteChangeQueryVO;
  success?: boolean;
}

/** 改签详情 */
export interface ShortRouteChangeQueryDetailBO {
  /** 改签航班日期 */
  rebookingDate?: string;
  /**
   * 改签日期价格
   * @format double
   */
  rebookingDateTotalPrice?: number;
  /** 改签票号 */
  ticketNos?: ShortRouteChangeQueryTicketBO[];
}

/** 改签票信息 */
export interface ShortRouteChangeQueryTicketBO {
  /** 改签票号 */
  ticketNo?: string;
  /**
   * 改日期价格
   * @format double
   */
  rebookingDatePrice?: number;
}

/** 改签查询返回 */
export interface ShortRouteChangeQueryVO {
  /** 航班信息 */
  flights?: FlightPlanBO[];
  /** 改签详情 */
  detail?: ShortRouteChangeQueryDetailBO;
}

/** 日历价格查询参数 */
export interface ShortRouteCalendarDTO {
  /** 出发日期 */
  startDate?: string;
  /** 到达日期 */
  endDate?: string;
  /** 出发地三字码 */
  depCode?: string;
  /** 到达地三字码 */
  arrCode?: string;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListShortRouteCalendarVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: ShortRouteCalendarVO[];
  success?: boolean;
}

/** 日历价格 */
export interface ShortRouteCalendarVO {
  /** 日期 */
  flightDate?: string;
  /**
   * 价格
   * @format double
   */
  price?: number;
}

/** 通用 API 返回结果 */
export interface CommonApiResultListCityInfoVO {
  /**
   * 状态码，200 表示成功
   * @format int32
   * @example 200
   */
  code?: number;
  /**
   * 返回信息
   * @example "成功"
   */
  message?: string;
  /** 返回数据 */
  data?: CityInfoVO[];
  success?: boolean;
}

import request from '../apiConfig';

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}
export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams extends Omit<any, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<FullRequestParams, 'body' | 'method' | 'query' | 'path'>;

/**
 * @title 低空商务平台APP
 * @version 1.0
 * @baseUrl http://**************:20000
 *
 * 散客API
 */
export class Api {
  travel = {
    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10SortieListCreate
     * @summary 获取架次列表
     * @request POST:/travel/v1.0/sortie/list
     */
    v10SortieListCreate: (data: TravelPackageSortieListDTO, params: RequestParams = {}) =>
      request<CommonApiResultListTravelSortieListVo, any>({
        path: `/travel/v1.0/sortie/list`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10RefundPriceQueryCreate
     * @summary 退票价格查询
     * @request POST:/travel/v1.0/refund/price/query
     */
    v10RefundPriceQueryCreate: (data: TravelRefundPriceDTO, params: RequestParams = {}) =>
      request<CommonApiResultTravelRefundPriceVo, any>({
        path: `/travel/v1.0/refund/price/query`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10RefundCreateCreate
     * @summary 退单创建
     * @request POST:/travel/v1.0/refund/create
     */
    v10RefundCreateCreate: (data: TravelRefundCreateDTO, params: RequestParams = {}) =>
      request<CommonApiResultTravelRefundOrderCreateVo, any>({
        path: `/travel/v1.0/refund/create`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10RefundConfirmCreate
     * @summary 退单确认
     * @request POST:/travel/v1.0/refund/confirm
     */
    v10RefundConfirmCreate: (data: TravelRefundConfirmDTO, params: RequestParams = {}) =>
      request<CommonApiResultTravelRefundOrderVo, any>({
        path: `/travel/v1.0/refund/confirm`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10ProductPurchaseInstructionsCreate
     * @summary 产品购买须知
     * @request POST:/travel/v1.0/product/purchaseInstructions
     */
    v10ProductPurchaseInstructionsCreate: (data: TravelProductNoDTO, params: RequestParams = {}) =>
      request<CommonApiResultListTravelProductPictureVo, any>({
        path: `/travel/v1.0/product/purchaseInstructions`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10ProductListCreate
     * @summary 产品列表查询
     * @request POST:/travel/v1.0/product/list
     */
    v10ProductListCreate: (data: TravelProductListDTO, params: RequestParams = {}) =>
      request<CommonApiResultPageResultListTravelProductListVO, any>({
        path: `/travel/v1.0/product/list`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10ProductGraphicIntroductionCreate
     * @summary 产品图文介绍
     * @request POST:/travel/v1.0/product/graphicIntroduction
     */
    v10ProductGraphicIntroductionCreate: (data: TravelProductNoDTO, params: RequestParams = {}) =>
      request<CommonApiResultListTravelProductPictureVo, any>({
        path: `/travel/v1.0/product/graphicIntroduction`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10PackageListCreate
     * @summary 获取套餐列表
     * @request POST:/travel/v1.0/package/list
     */
    v10PackageListCreate: (data: TravelPackageListDTO, params: RequestParams = {}) =>
      request<CommonApiResultListTravelPackageListVo, any>({
        path: `/travel/v1.0/package/list`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10PackageCalendarCreate
     * @summary 获取套餐库存日历
     * @request POST:/travel/v1.0/package/calendar
     */
    v10PackageCalendarCreate: (data: TravelPackageCalendarListDTO, params: RequestParams = {}) =>
      request<CommonApiResultListTravelPackageCalendarListVo, any>({
        path: `/travel/v1.0/package/calendar`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10CancelCreate
     * @summary 取消占座
     * @request POST:/travel/v1.0/cancel
     */
    v10CancelCreate: (data: TravelIssueOrCancelDTO, params: RequestParams = {}) =>
      request<CommonApiResultBoolean, any>({
        path: `/travel/v1.0/cancel`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10BookingCreate
     * @summary 占座
     * @request POST:/travel/v1.0/booking
     */
    v10BookingCreate: (data: TravelBookingDTO, params: RequestParams = {}) =>
      request<CommonApiResultTravelOrderVo, any>({
        path: `/travel/v1.0/booking`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 空中游览产品
     * @name V10IssueCreate
     * @summary 出票
     * @request POST:/travel/v1.0/Issue
     */
    v10IssueCreate: (data: TravelIssueOrCancelDTO, params: RequestParams = {}) =>
      request<CommonApiResultTravelOrderVo, any>({
        path: `/travel/v1.0/Issue`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),
  };
  common = {
    /**
     * @description 需要先验证图形验证码，然后发送短信验证码到指定手机号，验证码有5分钟有效期
     *
     * @tags 用户管理
     * @name V10SendSmsCodeCreate
     * @summary 发送登录短信验证码
     * @request POST:/common/v1.0/send-sms-code
     */
    v10SendSmsCodeCreate: (data: SmsCodeRequestDTO, params: RequestParams = {}) =>
      request<CommonApiResultString, any>({
        path: `/common/v1.0/send-sms-code`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户管理
     * @name V10RefreshCreate
     * @summary 刷新认证token
     * @request POST:/common/v1.0/refresh
     */
    v10RefreshCreate: (data: RefreshDTO, params: RequestParams = {}) =>
      request<CommonApiResultLoginVO, any>({
        path: `/common/v1.0/refresh`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户管理
     * @name V10LoginCreate
     * @summary 登录,登录成功后,accessToken 放到权限头里面,3次错误后需要重新获取短信验证码
     * @request POST:/common/v1.0/login
     */
    v10LoginCreate: (data: LoginDTO, params: RequestParams = {}) =>
      request<CommonApiResultLoginVO, any>({
        path: `/common/v1.0/login`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户管理
     * @name V10LoginOutCreate
     * @summary 退出登录
     * @request POST:/common/v1.0/loginOut
     */
    v10LoginOutCreate: (data: LoginOutDTO, params: RequestParams = {}) =>
      request<CommonApiResultVoid, any>({
        path: `/common/v1.0/loginOut`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 生成一个图形验证码，用于发送短信验证码前的验证
     *
     * @tags 用户管理
     * @name V10CaptchaImageCreate
     * @summary 获取图形验证码
     * @request POST:/common/v1.0/captcha-image
     */
    v10CaptchaImageCreate: (params: RequestParams = {}) =>
      request<CommonApiResultCaptchaImageDTO, any>({
        path: `/common/v1.0/captcha-image`,
        method: 'POST',
        ...params,
      }),
  };
  base = {
    /**
     * No description
     *
     * @tags 业务共用方法
     * @name V10PassengerUpdateCreate
     * @summary 修改常用乘机人信息
     * @request POST:/base/v1.0/passenger/update
     */
    v10PassengerUpdateCreate: (data: UpdateCommonPassengerDTO, params: RequestParams = {}) =>
      request<CommonApiResultString, any>({
        path: `/base/v1.0/passenger/update`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 业务共用方法
     * @name V10PassengerListCreate
     * @summary 查询常用乘机人信息
     * @request POST:/base/v1.0/passenger/list
     */
    v10PassengerListCreate: (params: RequestParams = {}) =>
      request<CommonApiResultListCommonPassengerListVO, any>({
        path: `/base/v1.0/passenger/list`,
        method: 'POST',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 业务共用方法
     * @name V10PassengerDeleteCreate
     * @summary 删除常用乘机人信息
     * @request POST:/base/v1.0/passenger/delete
     */
    v10PassengerDeleteCreate: (data: CommonPassengerDeleteDTO, params: RequestParams = {}) =>
      request<CommonApiResultString, any>({
        path: `/base/v1.0/passenger/delete`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 最多30人
     *
     * @tags 业务共用方法
     * @name V10PassengerAddCreate
     * @summary 添加常用乘机人信息
     * @request POST:/base/v1.0/passenger/add
     */
    v10PassengerAddCreate: (data: AddCommonPassengerDTO, params: RequestParams = {}) =>
      request<CommonApiResultString, any>({
        path: `/base/v1.0/passenger/add`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 业务共用方法
     * @name V10OrderListCreate
     * @summary 订单列表查询
     * @request POST:/base/v1.0/order/list
     */
    v10OrderListCreate: (data: CommonOrderListDTO, params: RequestParams = {}) =>
      request<CommonApiResultCommonOrderListVO, any>({
        path: `/base/v1.0/order/list`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 证件所属的国籍名称和代码
     *
     * @tags 业务共用方法
     * @name V10NationalityCreate
     * @summary 查询国籍代码
     * @request POST:/base/v1.0/nationality
     */
    v10NationalityCreate: (params: RequestParams = {}) =>
      request<CommonApiResultListNationalityVO, any>({
        path: `/base/v1.0/nationality`,
        method: 'POST',
        ...params,
      }),

    /**
     * @description 通过高德地图API获取城市名称，然后查询数据库获取城市ID
     *
     * @tags 业务共用方法
     * @name V10LocationCityCreate
     * @summary 根据经纬度查询城市名称和ID
     * @request POST:/base/v1.0/location/city
     */
    v10LocationCityCreate: (data: LocationDTO, params: RequestParams = {}) =>
      request<CommonApiResultCityInfoVO, any>({
        path: `/base/v1.0/location/city`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 业务共用方法
     * @name V10IndexBannerHotCreate
     * @summary 首页轮播图和热门推荐
     * @request POST:/base/v1.0/index/banner-hot
     */
    v10IndexBannerHotCreate: (data: BannerHotDTO, params: RequestParams = {}) =>
      request<CommonApiResultBannerHotVO, any>({
        path: `/base/v1.0/index/banner-hot`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 业务共用方法
     * @name ShortRouteV10PayCreate
     * @summary 订单支付
     * @request POST:/base/short-route/v1.0/pay
     */
    shortRouteV10PayCreate: (data: CommonOrderDTO, params: RequestParams = {}) =>
      request<CommonApiResultCommonOrderPayVO, any>({
        path: `/base/short-route/v1.0/pay`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 通过MinIO获取图片或文件，文件路径从URL中获取
     *
     * @tags 业务共用方法
     * @name V10FilesCommonList
     * @summary 访问图片或文件
     * @request GET:/base/v1.0/files/common/**
     */
    v10FilesCommonList: (params: RequestParams = {}) =>
      request<void, any>({
        path: `/base/v1.0/files/common/**`,
        method: 'GET',
        ...params,
      }),

    /**
     * @description 查询数据库中所有城市的ID和名称
     *
     * @tags 业务共用方法
     * @name V10CitiesList
     * @summary 查询所有城市信息
     * @request GET:/base/v1.0/cities
     */
    v10CitiesList: (params: RequestParams = {}) =>
      request<CommonApiResultListCityInfoVO, any>({
        path: `/base/v1.0/cities`,
        method: 'GET',
        ...params,
      }),
  };
  api = {
    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10TipCreate
     * @summary 短途运输统一提示
     * @request POST:/api/short-route/v1.0/tip
     */
    shortRouteV10TipCreate: (params: RequestParams = {}) =>
      request<CommonApiResultListShortRouteAppTipVO, any>({
        path: `/api/short-route/v1.0/tip`,
        method: 'POST',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10RefundCreate
     * @summary 申请自愿、非自愿退款
     * @request POST:/api/short-route/v1.0/refund
     */
    shortRouteV10RefundCreate: (data: ShortRouteRefundDTO, params: RequestParams = {}) =>
      request<CommonApiResultString, any>({
        path: `/api/short-route/v1.0/refund`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10RefundPriceCreate
     * @summary 退票价格查询
     * @request POST:/api/short-route/v1.0/refund/price
     */
    shortRouteV10RefundPriceCreate: (data: ShortRouteRefundPriceDTO, params: RequestParams = {}) =>
      request<CommonApiResultShortRouteRefundPriceVO, any>({
        path: `/api/short-route/v1.0/refund/price`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10RecommendCreate
     * @summary 短途航线推荐
     * @request POST:/api/short-route/v1.0/recommend
     */
    shortRouteV10RecommendCreate: (params: RequestParams = {}) =>
      request<CommonApiResultListShortRouteRecommendVO, any>({
        path: `/api/short-route/v1.0/recommend`,
        method: 'POST',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10PriceCreate
     * @summary 价格查询
     * @request POST:/api/short-route/v1.0/price
     */
    shortRouteV10PriceCreate: (data: ShortRoutePriceDTO, params: RequestParams = {}) =>
      request<CommonApiResultShortRoutePriceVO, any>({
        path: `/api/short-route/v1.0/price`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10OrderDetailCreate
     * @summary 订单详情查询
     * @request POST:/api/short-route/v1.0/order/detail
     */
    shortRouteV10OrderDetailCreate: (data: ShortRouteOrderDetailDTO, params: RequestParams = {}) =>
      request<CommonApiResultShortRouteOrderDetailVO, any>({
        path: `/api/short-route/v1.0/order/detail`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10OrderCancelCreate
     * @summary 取消订单
     * @request POST:/api/short-route/v1.0/order/cancel
     */
    shortRouteV10OrderCancelCreate: (data: ShortRouteOrderCancelDTO, params: RequestParams = {}) =>
      request<CommonApiResultString, any>({
        path: `/api/short-route/v1.0/order/cancel`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10OccupyCreate
     * @summary 占座
     * @request POST:/api/short-route/v1.0/occupy
     */
    shortRouteV10OccupyCreate: (data: ShortRouteOccupyDTO, params: RequestParams = {}) =>
      request<CommonApiResultShortRouteOccupyVO, any>({
        path: `/api/short-route/v1.0/occupy`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10FlightCreate
     * @summary 短途运输航班查询
     * @request POST:/api/short-route/v1.0/flight
     */
    shortRouteV10FlightCreate: (data: ShortRouteAvDTO, params: RequestParams = {}) =>
      request<CommonApiResultListFlightPlanBO, any>({
        path: `/api/short-route/v1.0/flight`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10FlightTipCreate
     * @summary 查询航班的提示
     * @request POST:/api/short-route/v1.0/flight/tip
     */
    shortRouteV10FlightTipCreate: (data: ShortRouteFlightTipDTO, params: RequestParams = {}) =>
      request<CommonApiResultListShortRouteFlightTipVO, any>({
        path: `/api/short-route/v1.0/flight/tip`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10CityCreate
     * @summary 短途航线城市查询
     * @request POST:/api/short-route/v1.0/city
     */
    shortRouteV10CityCreate: (params: RequestParams = {}) =>
      request<CommonApiResultShortRouteCityVO, any>({
        path: `/api/short-route/v1.0/city`,
        method: 'POST',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10ChangeCreate
     * @summary 改签
     * @request POST:/api/short-route/v1.0/change
     */
    shortRouteV10ChangeCreate: (data: ShortRouteChangeDTO, params: RequestParams = {}) =>
      request<CommonApiResultShortRouteChangeVO, any>({
        path: `/api/short-route/v1.0/change`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10ChangeQueryCreate
     * @summary 改签查询
     * @request POST:/api/short-route/v1.0/change/query
     */
    shortRouteV10ChangeQueryCreate: (data: ShortRouteChangeQueryDTO, params: RequestParams = {}) =>
      request<CommonApiResultShortRouteChangeQueryVO, any>({
        path: `/api/short-route/v1.0/change/query`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 短途运输产品
     * @name ShortRouteV10CalendarCreate
     * @summary 日历价格,最大日期不超过当前日期的3个月以后得时间，最小不小于当天，超出范围的时间，不会显示日期价格
     * @request POST:/api/short-route/v1.0/calendar
     */
    shortRouteV10CalendarCreate: (data: ShortRouteCalendarDTO, params: RequestParams = {}) =>
      request<CommonApiResultListShortRouteCalendarVO, any>({
        path: `/api/short-route/v1.0/calendar`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),
  };
}
