const { generateApi } = require('swagger-typescript-api');
const path = require('path');
const fs = require('fs/promises');

const outputDir = path.resolve(process.cwd(), './src/api/servies');

const readline = require('readline').createInterface({
  input: process.stdin,
  output: process.stdout,
});

const importAndExportApis = async (name, apiName) => {
  const filePath = path.resolve(process.cwd(), './src/api/index.ts');
  const originalContent = await fs.readFile(filePath, 'utf-8');
  const newContent =
    `import { Api as ${name}Api } from './servies/${name}';\nconst ${name}Apis = new ${name}Api().${apiName};\n${originalContent}`.replace(
      'export default {',
      `export default { ...${name}Apis,`,
    );
  if (
    originalContent.indexOf(
      `import { Api as ${name}Api } from './servies/${name}`,
    ) === -1
  ) {
    await fs.writeFile(filePath, newContent, 'utf-8');
  }
  console.log('API output successfully!');
};

const checkTsFile = async name => {
  const apiName = await new Promise(resolve => {
    readline.question(
      `请输入生成api类名如api、aiops一类进行对api/index导出的修改： `,
      resolve,
    );
  });
  await importAndExportApis(name, apiName);
  readline.close();
};

const swaggerUiOutput = async inputParams => {
  try {
    const outputParams = {
      ...inputParams,
      output: outputDir,
      templates: path.resolve(__dirname, './templates'),
      httpClientType: 'axios',
      routeTypes: true,
      cleanOutput: false,
    };
    await generateApi(outputParams);
    console.log('API client code generated successfully!');
    await checkTsFile(inputParams.name.replace('.ts', ''));
  } catch (error) {
    console.error(error);
  }
};

const getInputParams = async () => {
  const inputType = await new Promise(resolve => {
    readline.question('请输入需要生成脚本的方式(url/path)： ', resolve);
  });
  let inputParams = {};
  if (inputType === 'url') {
    inputParams.url = await new Promise(resolve => {
      readline.question('请输入swagger的在线url： ', resolve);
    });
  } else if (inputType === 'path') {
    inputParams.input = path.resolve(
      __dirname,
      await new Promise(resolve => {
        readline.question(
          '请输入swagger的本地路径如./swagger.json： ',
          resolve,
        );
      }),
    );
  } else {
    console.log('错误命令');
    readline.close();
    return;
  }
  inputParams.name = `${await new Promise(resolve => {
    readline.question('请输入生成api的name,如api： ', resolve);
  })}.ts`;
  await swaggerUiOutput(inputParams);
};

getInputParams();

/* NOTE: all fields are optional expect one of `output`, `url`, `spec` */
// generateApi({
//   input: path.resolve(__dirname, './swagger.json'),
//   // url: './swagger.json',
//   name: 'api.ts',
//   output: outputDir,
//   templates: path.resolve(__dirname, './templates'),
//   httpClientType: 'axios',
//   routeTypes: true,
//   cleanOutput: false,
// }).then(() => {
//   console.log('API client code generated successfully!');
// });

// 如果要重新生成模版文件的话可以启动，但是会丢失之前的修改内容
// generateTemplates({
//   cleanOutput: false,
//   output: path.resolve(process.cwd(), './templates'),
//   httpClientType: 'axios',
//   modular: false,
//   silent: false,
//   rewrite: false,
// });
