<%
const { contract, utils } = it;
const { formatDescription, require, _ } = utils;

%>
<% if (contract.$content.length) { %>
export type <%~ contract.name %> = {
  <% _.forEach(contract.$content, (field) => { %>
    <%~ includeFile('./object-field-jsdoc.ejs', { ...it, field }) %>
    <%~ field.field %>;
  <% }) %>
}<%~ utils.isNeedToAddNull(contract) ? ' | null' : ''%>
<% } else { %>
export type <%~ contract.name %> = Record<string, any>;
<% } %>
