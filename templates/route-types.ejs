<%
const { utils, config, routes, modelTypes } = it;
const { _, pascalCase } = utils;
const dataContracts = config.modular ? _.map(modelTypes, "name") : [];
%>

<% if (dataContracts.length) { %>
import { <%~ dataContracts.join(", ") %> } from "./<%~ config.fileNames.dataContracts %>"
<% } %>

<%
/* TODO: outOfModule, combined should be attributes of route, which will allow to avoid duplication of code */
%>

<% routes.outOfModule && routes.outOfModule.forEach(({ routes = [] }) => { %>
    <% routes.forEach((route) => { %>
        <%~ includeFile('./route-type.ejs', { ...it, route }) %>
    <% }) %>
<% }) %>

<% routes.combined && routes.combined.forEach(({ routes = [], moduleName }) => { %>
    export namespace <%~ pascalCase(moduleName) %> {
    <% routes.forEach((route) => { %>
        <%~ includeFile('./route-type.ejs', { ...it, route }) %>
    <% }) %>
    }

<% }) %>
